﻿using Fpi.Util;
using LibBaseModules.Helper;
using LibBusinessModules.Config;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using SqlSugar;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障记录管理界面
    /// </summary>
    public partial class UC_FaultRecordQuery : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前页数
        /// </summary>
        private int CurPage = 1;

        /// <summary>
        /// 总共页数
        /// </summary>
        private int PageCount;

        /// <summary>
        /// 每页记录数
        /// </summary>
        private int PageSize;

        /// <summary>
        /// SN码
        /// </summary>
        private string SnCode;

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 本次查到的总数据量
        /// </summary>
        private int RecordCount;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 设备因子缓存
        /// </summary>
        private readonly Dictionary<string, string> _factorCache = new Dictionary<string, string>();

        /// <summary>
        /// 故障类型名称缓存
        /// </summary>
        private readonly Dictionary<int, string> _category1NameCache = new Dictionary<int, string>();
        private readonly Dictionary<int, string> _category2NameCache = new Dictionary<int, string>();

        #endregion

        #region 构造函数

        public UC_FaultRecordQuery()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
                SetViewHead();

                // 禁用点击列表头排序
                foreach(DataGridViewColumn column in dgvRecords.Columns)
                {
                    column.SortMode = DataGridViewColumnSortMode.NotSortable;
                }
            }
        }

        /// <summary>
        /// 新增故障按钮点击事件
        /// </summary>
        private void btnAddFault_Click(object sender, EventArgs e)
        {
            try
            {
                new FrmFaultAdd().ShowDialog();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加故障信息出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            dgvRecords.Rows.Clear();
            CurPage = 1;
            lblPage.Text = @"?/?";

            // 清除缓存以确保数据最新
            ClearAllCache();

            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(!uint.TryParse(txtRecordCount.Text, out uint _))
                {
                    throw new FormatException("每页记录数应为整数，请重新输入！");
                }

                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                SnCode = txtSnCode.Text;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                PageSize = int.Parse(txtRecordCount.Text);

                // 查询数据数量
                try
                {
                    RecordCount = QueryDataCount();
                }
                catch(Exception ex)
                {
                    throw new Exception($"数据库连接异常:{ex.Message}");
                }

                if(RecordCount > 0)
                {
                    PageCount = (RecordCount - 1) / PageSize + 1;
                    QueryResult();
                }
                else
                {
                    PageCount = 0;
                    throw new Exception($"没有此时间段的故障记录！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
            dgvRecords.Focus();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if(dgvRecords.Rows.Count <= 0)
                {
                    throw new Exception("无数据可导出！");
                }
                else
                {
                    // 导出时弹窗提示选取导出目录，及文件名称
                    string filePath = Application.StartupPath + "\\export\\";
                    if(!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }

                    saveFileDialog.InitialDirectory = filePath;
                    saveFileDialog.FileName = $"{filePath}故障记录{StartTime:yyyy-MM-dd HH：mm：ss}--{EndTime:yyyy-MM-dd HH：mm：ss}.xlsx";

                    if(saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示等待界面
                        UIFormServiceHelper.ShowWaitForm(ParentForm, "数据导出中，请稍候...");

                        // 导出
                        FileExportHelper.SaveDataGridViewToExcelFile(ParentForm, dgvRecords, saveFileDialog.FileName);

                        // 隐藏等待界面
                        UIFormServiceHelper.HideWaitForm(ParentForm);

                        if(UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                        {
                            var psi = new ProcessStartInfo("Explorer.exe")
                            {
                                Arguments = "/e,/select," + saveFileDialog.FileName
                            };
                            // 打开导出文件所在位置
                            Process.Start(psi);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError(ex.Message);
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 右键菜单删除记录点击事件
        /// </summary>
        private void menuItemDelete_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedRows = dgvRecords.SelectedRows;
                if(selectedRows.Count == 0)
                {
                    UIMessageBox.ShowWarning("请选择要删除的记录！");
                    return;
                }

                if(!UIMessageBox.ShowAsk($"确定要删除选中的 {selectedRows.Count} 条记录吗？"))
                {
                    return;
                }

                var infosToDelete = new List<FaultRecordData>();
                foreach(DataGridViewRow row in selectedRows)
                {
                    if(row.Tag is FaultRecordData faultInfo)
                    {
                        infosToDelete.Add(faultInfo);
                    }
                }

                if(infosToDelete.Count > 0)
                {
                    var db = DBHelper.GetPCDBContext();
                    db.Deleteable(infosToDelete).ExecuteCommand();

                    UIMessageBox.ShowSuccess($"成功删除 {infosToDelete.Count} 条记录！");

                    // 刷新数据
                    btnStartQuery_Click(sender, e);
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"删除记录失败：{ex.Message}");
            }
        }

        #region 翻页事件

        private void btnFirst_Click(object sender, EventArgs e)
        {
            CurPage = 1;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if(CurPage > 1)
            {
                CurPage--;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if(CurPage < PageCount)
            {
                CurPage++;
                try
                {
                    QueryResult();
                }
                catch(Exception ex)
                {
                    UIMessageBox.ShowError("查询数据错误:" + ex.Message);
                }
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            if(PageCount <= 0)
                return;
            CurPage = PageCount;
            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 根据设备序列号获取因子信息（带缓存）
        /// </summary>
        /// <param name="snCode">设备序列号</param>
        /// <returns>因子信息</returns>
        private string GetFactorFromSNCode(string snCode)
        {
            if(string.IsNullOrEmpty(snCode))
                return "未知";

            try
            {
                // 根据序列号前6位获取设备配置
                if(snCode.Length >= 6)
                {
                    string idCode = snCode.Substring(0, 6);

                    // 先检查缓存
                    if(_factorCache.TryGetValue(idCode, out string cachedFactor))
                    {
                        return cachedFactor;
                    }

                    var deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(idCode);
                    if(deviceConfig != null)
                    {
                        string factor = deviceConfig.Factor;
                        // 缓存结果
                        _factorCache[idCode] = factor;
                        return factor;
                    }
                    else
                    {
                        // 缓存未知结果，避免重复查询
                        _factorCache[snCode] = "未知";
                    }
                }
            }
            catch(Exception)
            {
                // 记录错误但不影响显示
            }

            return "未知";
        }

        /// <summary>
        /// 根据一级分类ID获取名称（带缓存）
        /// </summary>
        /// <param name="category1Id">一级分类ID</param>
        /// <returns>一级分类名称</returns>
        private string GetCategory1NameById(int category1Id)
        {
            // 先检查缓存
            if(_category1NameCache.TryGetValue(category1Id, out string cachedName))
            {
                return cachedName;
            }

            // 获取名称并缓存
            string name = FaultManager.GetInstance().GetCategory1NameById(category1Id);
            _category1NameCache[category1Id] = name;
            return name;
        }

        /// <summary>
        /// 根据二级分类ID获取名称（带缓存）
        /// </summary>
        /// <param name="category2Id">二级分类ID</param>
        /// <returns>二级分类名称</returns>
        private string GetCategory2NameById(int category2Id)
        {
            // 先检查缓存
            if(_category2NameCache.TryGetValue(category2Id, out string cachedName))
            {
                return cachedName;
            }

            // 获取名称并缓存
            string name = FaultManager.GetInstance().GetCategory2NameById(category2Id);
            _category2NameCache[category2Id] = name;
            return name;
        }

        /// <summary>
        /// 清除所有缓存
        /// </summary>
        private void ClearAllCache()
        {
            _factorCache.Clear();
            _category1NameCache.Clear();
            _category2NameCache.Clear();
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 设置表头
        /// </summary>
        private void SetViewHead()
        {
            dgvRecords.Columns.Clear();
            dgvRecords.Columns.Add("Num", "序号");
            dgvRecords.Columns.Add("RecordTime", "录入时间");
            dgvRecords.Columns.Add("InstrumentId", "仪表ID");
            dgvRecords.Columns.Add("Factor", "因子");
            dgvRecords.Columns.Add("Category1", "类别1");
            dgvRecords.Columns.Add("Category2", "类别2");
            dgvRecords.Columns.Add("ProblemDescription", "问题描述");
        }

        /// <summary>
        /// 查询数据总数
        /// </summary>
        /// <returns></returns>
        private int QueryDataCount()
        {
            var query = DBHelper.GetPCDBContext().Queryable<FaultRecordData>()
                           .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime);

            // 添加查询条件
            if(!string.IsNullOrEmpty(SnCode))
            {
                query = query.Where(data => data.SNCode.Contains(SnCode));
            }

            return query.Count();
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            if(PageCount <= 0)
            {
                return;
            }

            lblPage.Text = $"{CurPage}/{PageCount}";

            FillDataToDgv();

            ManageEnable();
        }

        /// <summary>
        /// 填充数据到DataGridView
        /// </summary>
        private void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

                // 查询当前页数据
                var query = DBHelper.GetPCDBContext().Queryable<FaultRecordData>()
                               .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime);

                // 添加查询条件
                if(!string.IsNullOrEmpty(SnCode))
                {
                    query = query.Where(data => data.SNCode.Contains(SnCode));
                }

                // 按时间倒序排列
                var dataList = query.OrderBy(data => data.RecordTime, OrderByType.Desc)
                                   .ToPageList(CurPage - 1, PageSize);

                UIFormServiceHelper.HideWaitForm(this.ParentForm);

                dgvRecords.Rows.Clear();
                UIFormServiceHelper.ShowStatusForm(this.ParentForm, dataList.Count, "数据渲染中，请稍候...");

                int index = (CurPage - 1) * PageSize + 1;
                foreach(var data in dataList)
                {
                    int rowIndex = dgvRecords.AddRow();
                    DataGridViewRow dr = dgvRecords.Rows[rowIndex];
                    dr.Cells["Num"].Value = index;
                    dr.Cells["RecordTime"].Value = data.RecordTime.ToDisplayFormat();
                    dr.Cells["InstrumentId"].Value = data.SNCode;

                    // 动态获取因子信息
                    string factor = GetFactorFromSNCode(data.SNCode);
                    dr.Cells["Factor"].Value = factor;

                    // 根据ID获取故障类型名称（使用缓存）
                    string category1Name = GetCategory1NameById(data.Category1Id);
                    string category2Name = GetCategory2NameById(data.Category2Id);
                    dr.Cells["Category1"].Value = category1Name;
                    dr.Cells["Category2"].Value = category2Name;
                    dr.Cells["ProblemDescription"].Value = data.ProblemDescription;

                    dr.Tag = data;

                    UIFormServiceHelper.SetStatusFormDescription(this.ParentForm, $"数据渲染中[{index}/{RecordCount}]......");
                    UIFormServiceHelper.SetStatusFormStepIt(this.ParentForm, index);
                    index++;
                }
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 控制页数按钮的Enable属性
        /// </summary>
        private void ManageEnable()
        {
            if(CurPage == 1)
            {
                btnFirst.Enabled = btnPrev.Enabled = false;
            }
            else
            {
                btnFirst.Enabled = btnPrev.Enabled = true;
            }

            if(CurPage == PageCount)
            {
                btnEnd.Enabled = btnNext.Enabled = false;
            }
            else
            {
                btnEnd.Enabled = btnNext.Enabled = true;
            }
        }

        #endregion
    }
}