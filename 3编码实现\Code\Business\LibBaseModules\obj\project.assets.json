{"version": 3, "targets": {".NETFramework,Version=v4.8": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win-x64/native/comerr64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/gssapi64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/k5sprt64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krb5_64.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/krbcc64.dll": {"assetType": "native", "rid": "win-x64"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}}, ".NETFramework,Version=v4.8/win": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}}, ".NETFramework,Version=v4.8/win-arm64": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}}, ".NETFramework,Version=v4.8/win-x64": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "native": {"runtimes/win-x64/native/comerr64.dll": {}, "runtimes/win-x64/native/gssapi64.dll": {}, "runtimes/win-x64/native/k5sprt64.dll": {}, "runtimes/win-x64/native/krb5_64.dll": {}, "runtimes/win-x64/native/krbcc64.dll": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}}, ".NETFramework,Version=v4.8/win-x86": {"BouncyCastle.Cryptography/2.5.1": {"type": "package", "compile": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net461/BouncyCastle.Cryptography.dll": {"related": ".xml"}}}, "Enums.NET/4.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Enums.NET.dll": {"related": ".pdb;.xml"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "compile": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}, "runtime": {"lib/net48/ExtendedNumerics.BigDecimal.dll": {"related": ".xml"}}}, "Google.Protobuf/3.30.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Google.Protobuf.dll": {"related": ".pdb;.xml"}}}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.dll": {"related": ".xml"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8", "System.IO.Pipelines": "5.0.2"}, "compile": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Compression.LZ4.Streams.dll": {"related": ".xml"}}}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}, "runtime": {"lib/net462/K4os.Hash.xxHash.dll": {"related": ".xml"}}}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "compile": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MathNet.Numerics.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "MySql.Data/9.3.0": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.5.1", "Google.Protobuf": "3.30.0", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "ZstdSharp.Port": "0.8.5"}, "frameworkAssemblies": ["System", "System.Configuration", "System.Data", "System.Management", "System.Transactions"], "compile": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net48/MySql.Data.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NPOI/2.7.3": {"type": "package", "dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/NPOI.Core.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OOXML.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXml4Net.dll": {"related": ".pdb;.xml"}, "lib/net472/NPOI.OpenXmlFormats.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "SixLabors.Fonts/1.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "compile": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/SixLabors.ImageSharp.dll": {"related": ".xml"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "SunnyUI/3.8.2": {"type": "package", "dependencies": {"SunnyUI.Common": "3.8.2"}, "frameworkAssemblies": ["System.Design", "System.Web.Extensions", "System.Windows.Forms"], "compile": {"lib/net472/SunnyUI.dll": {}}, "runtime": {"lib/net472/SunnyUI.dll": {}}}, "SunnyUI.Common/3.8.2": {"type": "package", "compile": {"lib/net472/SunnyUI.Common.dll": {}}, "runtime": {"lib/net472/SunnyUI.Common.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipelines/5.0.2": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.8.5": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/ZstdSharp.dll": {}}, "runtime": {"lib/net462/ZstdSharp.dll": {}}}}}, "libraries": {"BouncyCastle.Cryptography/2.5.1": {"sha512": "zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "type": "package", "path": "bouncycastle.cryptography/2.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "bouncycastle.cryptography.2.5.1.nupkg.sha512", "bouncycastle.cryptography.nuspec", "lib/net461/BouncyCastle.Cryptography.dll", "lib/net461/BouncyCastle.Cryptography.xml", "lib/net6.0/BouncyCastle.Cryptography.dll", "lib/net6.0/BouncyCastle.Cryptography.xml", "lib/netstandard2.0/BouncyCastle.Cryptography.dll", "lib/netstandard2.0/BouncyCastle.Cryptography.xml", "packageIcon.png"]}, "Enums.NET/4.0.1": {"sha512": "OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "type": "package", "path": "enums.net/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "enums.net.4.0.1.nupkg.sha512", "enums.net.nuspec", "lib/net45/Enums.NET.dll", "lib/net45/Enums.NET.pdb", "lib/net45/Enums.NET.xml", "lib/netcoreapp3.0/Enums.NET.dll", "lib/netcoreapp3.0/Enums.NET.pdb", "lib/netcoreapp3.0/Enums.NET.xml", "lib/netstandard1.0/Enums.NET.dll", "lib/netstandard1.0/Enums.NET.pdb", "lib/netstandard1.0/Enums.NET.xml", "lib/netstandard1.1/Enums.NET.dll", "lib/netstandard1.1/Enums.NET.pdb", "lib/netstandard1.1/Enums.NET.xml", "lib/netstandard1.3/Enums.NET.dll", "lib/netstandard1.3/Enums.NET.pdb", "lib/netstandard1.3/Enums.NET.xml", "lib/netstandard2.0/Enums.NET.dll", "lib/netstandard2.0/Enums.NET.pdb", "lib/netstandard2.0/Enums.NET.xml", "lib/netstandard2.1/Enums.NET.dll", "lib/netstandard2.1/Enums.NET.pdb", "lib/netstandard2.1/Enums.NET.xml"]}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"sha512": "+woGT1lsBtwkntOpx2EZbdbySv0aWPefE0vrfvclxVdbi4oa2bbtphFPWgMiQe+kRCPICbfFJwp6w1DuR7Ge2Q==", "type": "package", "path": "extendednumerics.bigdecimal/2025.1001.2.129", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512", "extendednumerics.bigdecimal.nuspec", "lib/net45/ExtendedNumerics.BigDecimal.dll", "lib/net45/ExtendedNumerics.BigDecimal.xml", "lib/net46/ExtendedNumerics.BigDecimal.dll", "lib/net46/ExtendedNumerics.BigDecimal.xml", "lib/net472/ExtendedNumerics.BigDecimal.dll", "lib/net472/ExtendedNumerics.BigDecimal.xml", "lib/net48/ExtendedNumerics.BigDecimal.dll", "lib/net48/ExtendedNumerics.BigDecimal.xml", "lib/net5.0/ExtendedNumerics.BigDecimal.dll", "lib/net5.0/ExtendedNumerics.BigDecimal.xml", "lib/net6.0/ExtendedNumerics.BigDecimal.dll", "lib/net6.0/ExtendedNumerics.BigDecimal.xml", "lib/net7.0/ExtendedNumerics.BigDecimal.dll", "lib/net7.0/ExtendedNumerics.BigDecimal.xml", "lib/net8.0/ExtendedNumerics.BigDecimal.dll", "lib/net8.0/ExtendedNumerics.BigDecimal.xml", "lib/netcoreapp3.1/ExtendedNumerics.BigDecimal.dll", "lib/netcoreapp3.1/ExtendedNumerics.BigDecimal.xml", "lib/netstandard2.0/ExtendedNumerics.BigDecimal.dll", "lib/netstandard2.0/ExtendedNumerics.BigDecimal.xml", "lib/netstandard2.1/ExtendedNumerics.BigDecimal.dll", "lib/netstandard2.1/ExtendedNumerics.BigDecimal.xml"]}, "Google.Protobuf/3.30.0": {"sha512": "ZnEI4oZWnHvd+Yz5Gcnx5Q5RQIuzptIzd0fmxAN8f81FYHI0USZqMOrPTkrsd/QEzo9vl2b217v9FqFgHfufQw==", "type": "package", "path": "google.protobuf/3.30.0", "files": [".nupkg.metadata", ".signature.p7s", "google.protobuf.3.30.0.nupkg.sha512", "google.protobuf.nuspec", "lib/net45/Google.Protobuf.dll", "lib/net45/Google.Protobuf.pdb", "lib/net45/Google.Protobuf.xml", "lib/net5.0/Google.Protobuf.dll", "lib/net5.0/Google.Protobuf.pdb", "lib/net5.0/Google.Protobuf.xml", "lib/netstandard1.1/Google.Protobuf.dll", "lib/netstandard1.1/Google.Protobuf.pdb", "lib/netstandard1.1/Google.Protobuf.xml", "lib/netstandard2.0/Google.Protobuf.dll", "lib/netstandard2.0/Google.Protobuf.pdb", "lib/netstandard2.0/Google.Protobuf.xml"]}, "K4os.Compression.LZ4/1.3.8": {"sha512": "LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "type": "package", "path": "k4os.compression.lz4/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.1.3.8.nupkg.sha512", "k4os.compression.lz4.nuspec", "lib/net462/K4os.Compression.LZ4.dll", "lib/net462/K4os.Compression.LZ4.xml", "lib/net5.0/K4os.Compression.LZ4.dll", "lib/net5.0/K4os.Compression.LZ4.xml", "lib/net6.0/K4os.Compression.LZ4.dll", "lib/net6.0/K4os.Compression.LZ4.xml", "lib/netstandard2.0/K4os.Compression.LZ4.dll", "lib/netstandard2.0/K4os.Compression.LZ4.xml", "lib/netstandard2.1/K4os.Compression.LZ4.dll", "lib/netstandard2.1/K4os.Compression.LZ4.xml"]}, "K4os.Compression.LZ4.Streams/1.3.8": {"sha512": "P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "type": "package", "path": "k4os.compression.lz4.streams/1.3.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.compression.lz4.streams.1.3.8.nupkg.sha512", "k4os.compression.lz4.streams.nuspec", "lib/net462/K4os.Compression.LZ4.Streams.dll", "lib/net462/K4os.Compression.LZ4.Streams.xml", "lib/net5.0/K4os.Compression.LZ4.Streams.dll", "lib/net5.0/K4os.Compression.LZ4.Streams.xml", "lib/net6.0/K4os.Compression.LZ4.Streams.dll", "lib/net6.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.0/K4os.Compression.LZ4.Streams.xml", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.dll", "lib/netstandard2.1/K4os.Compression.LZ4.Streams.xml"]}, "K4os.Hash.xxHash/1.0.8": {"sha512": "Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "type": "package", "path": "k4os.hash.xxhash/1.0.8", "files": [".nupkg.metadata", ".signature.p7s", "k4os.hash.xxhash.1.0.8.nupkg.sha512", "k4os.hash.xxhash.nuspec", "lib/net462/K4os.Hash.xxHash.dll", "lib/net462/K4os.Hash.xxHash.xml", "lib/net5.0/K4os.Hash.xxHash.dll", "lib/net5.0/K4os.Hash.xxHash.xml", "lib/net6.0/K4os.Hash.xxHash.dll", "lib/net6.0/K4os.Hash.xxHash.xml", "lib/netstandard2.0/K4os.Hash.xxHash.dll", "lib/netstandard2.0/K4os.Hash.xxHash.xml", "lib/netstandard2.1/K4os.Hash.xxHash.dll", "lib/netstandard2.1/K4os.Hash.xxHash.xml"]}, "MathNet.Numerics.Signed/5.0.0": {"sha512": "PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "type": "package", "path": "mathnet.numerics.signed/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/MathNet.Numerics.dll", "lib/net461/MathNet.Numerics.xml", "lib/net48/MathNet.Numerics.dll", "lib/net48/MathNet.Numerics.xml", "lib/net5.0/MathNet.Numerics.dll", "lib/net5.0/MathNet.Numerics.xml", "lib/net6.0/MathNet.Numerics.dll", "lib/net6.0/MathNet.Numerics.xml", "lib/netstandard2.0/MathNet.Numerics.dll", "lib/netstandard2.0/MathNet.Numerics.xml", "mathnet.numerics.signed.5.0.0.nupkg.sha512", "mathnet.numerics.signed.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"sha512": "W8DPQjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"sha512": "irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "type": "package", "path": "microsoft.io.recyclablememorystream/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "MySql.Data/9.3.0": {"sha512": "f4iZBKubzwNnc84ecEwguv5TSwHpoArFGa+XpZY+8S9SoAnQaHfymLdZUKK4LbUbH9ACZ6X71LUvq58Mfdyipw==", "type": "package", "path": "mysql.data/9.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README", "README.md", "lib/net462/MySql.Data.dll", "lib/net462/MySql.Data.xml", "lib/net48/MySql.Data.dll", "lib/net48/MySql.Data.xml", "lib/net8.0/MySql.Data.dll", "lib/net8.0/MySql.Data.xml", "lib/net9.0/MySql.Data.dll", "lib/net9.0/MySql.Data.xml", "lib/netstandard2.0/MySql.Data.dll", "lib/netstandard2.0/MySql.Data.xml", "lib/netstandard2.1/MySql.Data.dll", "lib/netstandard2.1/MySql.Data.xml", "logo-mysql-170x115.png", "mysql.data.9.3.0.nupkg.sha512", "mysql.data.nuspec", "runtimes/win-x64/native/comerr64.dll", "runtimes/win-x64/native/gssapi64.dll", "runtimes/win-x64/native/k5sprt64.dll", "runtimes/win-x64/native/krb5_64.dll", "runtimes/win-x64/native/krbcc64.dll"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NPOI/2.7.3": {"sha512": "iCZx3DSwUSwaV61E8tXgPlPuxYmcYV/Zi405nGlxQvWaGTAbuc0KvSBjsLucQUJ92iMeetT8iK9makLfF4uZ3g==", "type": "package", "path": "npoi/2.7.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "lib/net472/NPOI.Core.dll", "lib/net472/NPOI.Core.pdb", "lib/net472/NPOI.Core.xml", "lib/net472/NPOI.OOXML.dll", "lib/net472/NPOI.OOXML.pdb", "lib/net472/NPOI.OOXML.xml", "lib/net472/NPOI.OpenXml4Net.dll", "lib/net472/NPOI.OpenXml4Net.pdb", "lib/net472/NPOI.OpenXml4Net.xml", "lib/net472/NPOI.OpenXmlFormats.dll", "lib/net472/NPOI.OpenXmlFormats.pdb", "lib/net472/NPOI.OpenXmlFormats.xml", "lib/net8.0/NPOI.Core.dll", "lib/net8.0/NPOI.Core.pdb", "lib/net8.0/NPOI.Core.xml", "lib/net8.0/NPOI.OOXML.dll", "lib/net8.0/NPOI.OOXML.pdb", "lib/net8.0/NPOI.OOXML.xml", "lib/net8.0/NPOI.OpenXml4Net.dll", "lib/net8.0/NPOI.OpenXml4Net.pdb", "lib/net8.0/NPOI.OpenXml4Net.xml", "lib/net8.0/NPOI.OpenXmlFormats.dll", "lib/net8.0/NPOI.OpenXmlFormats.pdb", "lib/net8.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.0/NPOI.Core.dll", "lib/netstandard2.0/NPOI.Core.pdb", "lib/netstandard2.0/NPOI.Core.xml", "lib/netstandard2.0/NPOI.OOXML.dll", "lib/netstandard2.0/NPOI.OOXML.pdb", "lib/netstandard2.0/NPOI.OOXML.xml", "lib/netstandard2.0/NPOI.OpenXml4Net.dll", "lib/netstandard2.0/NPOI.OpenXml4Net.pdb", "lib/netstandard2.0/NPOI.OpenXml4Net.xml", "lib/netstandard2.0/NPOI.OpenXmlFormats.dll", "lib/netstandard2.0/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.0/NPOI.OpenXmlFormats.xml", "lib/netstandard2.1/NPOI.Core.dll", "lib/netstandard2.1/NPOI.Core.pdb", "lib/netstandard2.1/NPOI.Core.xml", "lib/netstandard2.1/NPOI.OOXML.dll", "lib/netstandard2.1/NPOI.OOXML.pdb", "lib/netstandard2.1/NPOI.OOXML.xml", "lib/netstandard2.1/NPOI.OpenXml4Net.dll", "lib/netstandard2.1/NPOI.OpenXml4Net.pdb", "lib/netstandard2.1/NPOI.OpenXml4Net.xml", "lib/netstandard2.1/NPOI.OpenXmlFormats.dll", "lib/netstandard2.1/NPOI.OpenXmlFormats.pdb", "lib/netstandard2.1/NPOI.OpenXmlFormats.xml", "logo/120_120.jpg", "logo/240_240.png", "logo/32_32.jpg", "logo/60_60.jpg", "npoi.2.7.3.nupkg.sha512", "npoi.nuspec"]}, "SharpZipLib/1.4.2": {"sha512": "yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "type": "package", "path": "sharpziplib/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.2.nupkg.sha512", "sharpziplib.nuspec"]}, "SixLabors.Fonts/1.0.1": {"sha512": "ljezRHWc7N0azdQViib7Aa5v+DagRVkKI2/93kEbtjVczLs+yTkSq6gtGmvOcx4IqyNbO3GjLt7SAQTpLkySNw==", "type": "package", "path": "sixlabors.fonts/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.1.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/2.1.10": {"sha512": "hk1E7U3RSlxrBVo6Gb6OjeM52fChpFYH+SZvyT/M2vzSGlzAaKE33hc3V/Pvnjcnn1opT8/Z+0QfqdM5HsIaeA==", "type": "package", "path": "sixlabors.imagesharp/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/SixLabors.ImageSharp.dll", "lib/net472/SixLabors.ImageSharp.xml", "lib/netcoreapp2.1/SixLabors.ImageSharp.dll", "lib/netcoreapp2.1/SixLabors.ImageSharp.xml", "lib/netcoreapp3.1/SixLabors.ImageSharp.dll", "lib/netcoreapp3.1/SixLabors.ImageSharp.xml", "lib/netstandard2.0/SixLabors.ImageSharp.dll", "lib/netstandard2.0/SixLabors.ImageSharp.xml", "lib/netstandard2.1/SixLabors.ImageSharp.dll", "lib/netstandard2.1/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.2.1.10.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SqlSugar/*********": {"sha512": "I8UTddKmYE13y+pbmigsxZ4KdSzHnRW53uT+MTgxax6zIfyvNDWFoy4cOnDlzo8+KWCBsRNORhfKcBUWZZGBQg==", "type": "package", "path": "sqlsugar/*********", "files": [".nupkg.metadata", ".signature.p7s", "lib/SqlSugar.dll", "sqlsugar.*********.nupkg.sha512", "sqlsugar.nuspec"]}, "SunnyUI/3.8.2": {"sha512": "CnZ1qpuQtY14jF2RXwS219wPbJFeZHarrEy04p2+oYQL4SeeYpMRwmMnBSUehRAj1cR5WCNHxUF/bH7tCacz7g==", "type": "package", "path": "sunnyui/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.dll", "lib/net472/SunnyUI.dll", "lib/net8.0-windows7.0/SunnyUI.dll", "lib/net9.0-windows7.0/SunnyUI.dll", "sunnyui.3.8.2.nupkg.sha512", "sunnyui.nuspec"]}, "SunnyUI.Common/3.8.2": {"sha512": "OIz2kvCX3HLYPJLilUV7rnK34tyg3DDwz+O0omGIE1tIBoOlrkZllmTWhQz7DbwzECefyCCW/4rPB088EiMocQ==", "type": "package", "path": "sunnyui.common/3.8.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "SunnyUI.png", "lib/net40/SunnyUI.Common.dll", "lib/net472/SunnyUI.Common.dll", "lib/net8.0/SunnyUI.Common.dll", "lib/net9.0/SunnyUI.Common.dll", "sunnyui.common.3.8.2.nupkg.sha512", "sunnyui.common.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.1": {"sha512": "vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/5.0.2": {"sha512": "Iew+dfa6FFiyvWBdRmXApixRY1db+beyutpIck4SOSe0NLM8FD/7AD54MscqVLhvfSMLHO7KadjTRT7fqxOGTA==", "type": "package", "path": "system.io.pipelines/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.2.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/8.0.2": {"sha512": "aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "type": "package", "path": "system.security.cryptography.xml/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/net7.0/System.Security.Cryptography.Xml.dll", "lib/net7.0/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.8.0.2.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/5.0.0": {"sha512": "NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "type": "package", "path": "system.text.encoding.codepages/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.5.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "ZstdSharp.Port/0.8.5": {"sha512": "TR4j17WeVSEb3ncgL2NqlXEqcy04I+Kk9CaebNDplUeL8XOgjkZ7fP4Wg4grBdPLIqsV86p2QaXTkZoRMVOsew==", "type": "package", "path": "zstdsharp.port/0.8.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/net8.0/ZstdSharp.dll", "lib/net9.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.8.5.nupkg.sha512", "zstdsharp.port.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["MySql.Data >= 9.3.0", "NPOI >= 2.7.3", "Newtonsoft.Json >= 13.0.3", "SqlSugar >= *********", "SunnyUI >= 3.8.2"]}, "packageFolders": {"F:\\Nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj", "projectName": "LibBaseModules", "projectPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\LibBaseModules.csproj", "packagesPath": "F:\\Nuget\\packages\\", "outputPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBaseModules\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://pcnuget.fpi-inc.com/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SqlSugar": {"target": "Package", "version": "[*********, )"}, "SunnyUI": {"target": "Package", "version": "[3.8.2, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}