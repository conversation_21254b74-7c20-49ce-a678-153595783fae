using LibBusinessModules.Config.FaultManagement.Models;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.FaultManagement.UI
{
    /// <summary>
    /// 故障分类管理界面 - 树形结构
    /// </summary>
    public partial class UC_FaultCategoryManager : UIUserControl
    {
        #region 字段

        /// <summary>
        /// 当前选中的节点
        /// </summary>
        private TreeNode _selectedNode;

        /// <summary>
        /// 当前编辑模式
        /// </summary>
        private EditMode _currentEditMode = EditMode.None;

        /// <summary>
        /// 根节点名称
        /// </summary>
        private const string ROOT_NODE_TEXT = "故障类别";

        #endregion

        #region 构造

        public UC_FaultCategoryManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_FaultCategoryManager_Load(object sender, EventArgs e)
        {
            if(!DesignMode)
            {
                InitializeTreeView();
                RefreshTreeView();
                ClearDetailPanel();
                UpdateDetailPanelVisibility(null);
            }
        }

        /// <summary>
        /// 树形控件节点选择事件
        /// </summary>
        private void treeViewCategory_AfterSelect(object sender, TreeViewEventArgs e)
        {
            // 检查当前节点是否有未完成的编辑
            if (!ValidateCurrentNodeBeforeSwitch())
            {
                // 阻止切换，恢复到原来的选中节点
                treeViewCategory.AfterSelect -= treeViewCategory_AfterSelect;
                treeViewCategory.SelectedNode = _selectedNode;
                treeViewCategory.AfterSelect += treeViewCategory_AfterSelect;
                return;
            }

            _selectedNode = e.Node;
            LoadNodeDetails(e.Node);
            UpdateDetailPanelVisibility(e.Node);
        }

        /// <summary>
        /// 树形控件右键菜单打开前事件 - 根据节点类型动态显示菜单项
        /// </summary>
        private void contextMenuStrip_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 获取右键点击的节点
            Point point = treeViewCategory.PointToClient(Cursor.Position);
            TreeNode clickedNode = treeViewCategory.GetNodeAt(point);

            if(clickedNode == null)
            {
                e.Cancel = true; // 取消显示菜单
                return;
            }

            // 如果要切换到不同的节点，先验证当前节点
            if(clickedNode != _selectedNode && !ValidateCurrentNodeBeforeSwitch())
            {
                e.Cancel = true; // 取消显示菜单
                return;
            }

            // 根据节点类型显示不同的菜单项
            if(clickedNode.Tag == null) // 根节点
            {
                menuItemAdd.Visible = true;
                menuItemAdd.Text = "新增一级分类";
                menuItemEdit.Visible = false;
                menuItemDelete.Visible = false;
            }
            else if(clickedNode.Tag is FaultCategory1) // 一级分类节点
            {
                menuItemAdd.Visible = true;
                menuItemAdd.Text = "新增二级分类";
                menuItemEdit.Visible = false;
                menuItemDelete.Visible = true;
                menuItemDelete.Text = "删除一级分类";
            }
            else if(clickedNode.Tag is FaultCategory2) // 二级分类节点
            {
                menuItemAdd.Visible = false;
                menuItemEdit.Visible = false;
                menuItemDelete.Visible = true;
                menuItemDelete.Text = "删除二级分类";
            }

            // 设置当前右键点击的节点为选中节点（如果验证通过）
            if(clickedNode != _selectedNode)
            {
                treeViewCategory.SelectedNode = clickedNode;
                _selectedNode = clickedNode;
                LoadNodeDetails(clickedNode);
                UpdateDetailPanelVisibility(clickedNode);
            }
        }

        /// <summary>
        /// 右键菜单 - 新增
        /// </summary>
        private void menuItemAdd_Click(object sender, EventArgs e)
        {
            AddCategory();
        }

        /// <summary>
        /// 右键菜单 - 编辑（已移除，保留方法以防设计器引用）
        /// </summary>
        private void menuItemEdit_Click(object sender, EventArgs e)
        {
            // 编辑功能已移除，直接在右侧面板编辑
        }

        /// <summary>
        /// 右键菜单 - 删除
        /// </summary>
        private void menuItemDelete_Click(object sender, EventArgs e)
        {
            DeleteCategory();
        }



        /// <summary>
        /// 保存修改
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            if(SaveCurrentEdit())
            {
                FaultManager.GetInstance().SaveConfig();
                UIMessageBox.ShowSuccess("保存成功！");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if(UIMessageBox.ShowAsk("确定要恢复所有修改吗？"))
            {
                FaultManager.GetInstance().ReLoad();

                // 清理选中状态
                _selectedNode = null;
                treeViewCategory.SelectedNode = null;

                RefreshTreeView();
                ClearDetailPanel();
                UpdateDetailPanelVisibility(null);
                UIMessageBox.ShowSuccess("重置修改成功！");
            }
        }

        /// <summary>
        /// 分类名称文本变化事件 - 实现左右联动和实时保存
        /// </summary>
        private void txtCategoryName_TextChanged(object sender, EventArgs e)
        {
            if(_selectedNode != null && _selectedNode.Tag != null)
            {
                // 更新树节点显示的文本
                _selectedNode.Text = txtCategoryName.Text;

                // 同时更新数据模型
                if(_selectedNode.Tag is FaultCategory1 category1)
                {
                    category1.FaultName = txtCategoryName.Text;
                }
                else if(_selectedNode.Tag is FaultCategory2 category2)
                {
                    category2.FaultName = txtCategoryName.Text;
                }
            }
        }

        /// <summary>
        /// 分类描述文本变化事件 - 实现实时保存
        /// </summary>
        private void txtDescription_TextChanged(object sender, EventArgs e)
        {
            if(_selectedNode != null && _selectedNode.Tag != null)
            {
                // 更新数据模型
                if(_selectedNode.Tag is FaultCategory1 category1)
                {
                    category1.Description = txtDescription.Text;
                }
                else if(_selectedNode.Tag is FaultCategory2 category2)
                {
                    category2.Description = txtDescription.Text;
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证当前节点在切换前是否满足条件
        /// </summary>
        private bool ValidateCurrentNodeBeforeSwitch()
        {
            // 如果当前没有选中的节点或选中的是根节点，允许切换
            if(_selectedNode == null || _selectedNode.Tag == null)
            {
                return true;
            }

            // 检查TreeView的实际选中状态，如果TreeView没有选中节点，说明是删除或重置后的状态
            if(treeViewCategory.SelectedNode == null)
            {
                // 清理内部状态，允许切换
                _selectedNode = null;
                return true;
            }

            // 检查当前节点的故障名称是否为空
            if(string.IsNullOrWhiteSpace(txtCategoryName.Text))
            {
                UIMessageBox.ShowError("当前节点名称不可为空，请先完成编辑！");

                // 聚焦到名称输入框
                txtCategoryName.Focus();
                txtCategoryName.SelectAll();

                return false;
            }

            return true;
        }

        /// <summary>
        /// 更新详情面板的显示状态
        /// </summary>
        private void UpdateDetailPanelVisibility(TreeNode node)
        {
            if(node == null || node.Tag == null)
            {
                // 未选中节点或选中根节点时隐藏详情面板
                gbDetail.Visible = false;
            }
            else
            {
                // 选中一级或二级分类节点时显示详情面板
                gbDetail.Visible = true;
            }
        }

        /// <summary>
        /// 初始化树形控件
        /// </summary>
        private void InitializeTreeView()
        {
            treeViewCategory.Nodes.Clear();

            // 创建根节点
            TreeNode rootNode = new TreeNode(ROOT_NODE_TEXT)
            {
                Tag = null, // 根节点没有关联数据
                ImageIndex = 0,
                SelectedImageIndex = 0
            };

            treeViewCategory.Nodes.Add(rootNode);
            rootNode.Expand();
        }

        /// <summary>
        /// 刷新树形结构
        /// </summary>
        private void RefreshTreeView()
        {
            if(treeViewCategory.Nodes.Count == 0)
            {
                InitializeTreeView();
            }

            TreeNode rootNode = treeViewCategory.Nodes[0];
            rootNode.Nodes.Clear();

            // 加载一级分类
            List<FaultCategory1> categories = FaultManager.GetInstance().GetAllCategory1();
            foreach(var category1 in categories)
            {
                TreeNode category1Node = new TreeNode(category1.FaultName)
                {
                    Tag = category1,
                    ImageIndex = 1,
                    SelectedImageIndex = 1
                };

                // 加载二级分类
                foreach(var category2 in category1.SubCategories.OrderBy(x => x.FaultId))
                {
                    TreeNode category2Node = new TreeNode(category2.FaultName)
                    {
                        Tag = category2,
                        ImageIndex = 2,
                        SelectedImageIndex = 2
                    };
                    category1Node.Nodes.Add(category2Node);
                }

                rootNode.Nodes.Add(category1Node);
                category1Node.Expand();
            }

            rootNode.Expand();
        }

        /// <summary>
        /// 加载节点详细信息到右侧面板
        /// </summary>
        private void LoadNodeDetails(TreeNode node)
        {
            // 临时移除事件处理，避免循环更新
            txtCategoryName.TextChanged -= txtCategoryName_TextChanged;
            txtDescription.TextChanged -= txtDescription_TextChanged;

            try
            {
                if(node == null)
                {
                    ClearDetailPanel();
                    return;
                }

                if(node.Tag is FaultCategory1 category1)
                {
                    // 一级分类
                    txtCategoryId.Text = category1.FaultId.ToString();
                    txtCategoryName.Text = category1.FaultName;
                    txtDescription.Text = category1.Description;
                    txtParentCategory.Text = "";
                    txtParentCategory.Visible = false;
                    uiLabel4.Visible = false;

                    _currentEditMode = EditMode.EditLevel1;
                }
                else if(node.Tag is FaultCategory2 category2)
                {
                    // 二级分类
                    txtCategoryId.Text = category2.FaultId.ToString();
                    txtCategoryName.Text = category2.FaultName;
                    txtDescription.Text = category2.Description;

                    // 显示所属一级分类
                    var parentCategory = FaultManager.GetInstance().GetCategory1ById(category2.ParentFaultId);
                    txtParentCategory.Text = parentCategory?.FaultName ?? "";
                    txtParentCategory.Visible = true;
                    uiLabel4.Visible = true;

                    _currentEditMode = EditMode.EditLevel2;
                }
                else
                {
                    // 根节点
                    ClearDetailPanel();
                    _currentEditMode = EditMode.None;
                }
            }
            finally
            {
                // 重新添加事件处理
                txtCategoryName.TextChanged += txtCategoryName_TextChanged;
                txtDescription.TextChanged += txtDescription_TextChanged;
            }
        }

        /// <summary>
        /// 清空详细信息面板
        /// </summary>
        private void ClearDetailPanel()
        {
            // 临时移除事件处理，避免触发不必要的更新
            txtCategoryName.TextChanged -= txtCategoryName_TextChanged;
            txtDescription.TextChanged -= txtDescription_TextChanged;

            try
            {
                txtCategoryId.Text = "";
                txtCategoryName.Text = "";
                txtDescription.Text = "";
                txtParentCategory.Text = "";
                txtParentCategory.Visible = false;
                uiLabel4.Visible = false;
                _currentEditMode = EditMode.None;
            }
            finally
            {
                // 重新添加事件处理
                txtCategoryName.TextChanged += txtCategoryName_TextChanged;
                txtDescription.TextChanged += txtDescription_TextChanged;
            }
        }

        /// <summary>
        /// 新增分类
        /// </summary>
        private void AddCategory()
        {
            if(_selectedNode == null) return;

            if(_selectedNode.Tag == null) // 根节点，新增一级分类
            {
                AddLevel1Category();
            }
            else if(_selectedNode.Tag is FaultCategory1) // 一级分类节点，新增二级分类
            {
                AddLevel2Category((FaultCategory1)_selectedNode.Tag);
            }
            // 二级分类节点不能新增子节点
        }



        /// <summary>
        /// 删除分类
        /// </summary>
        private void DeleteCategory()
        {
            if(_selectedNode == null || _selectedNode.Tag == null) return;

            if(_selectedNode.Tag is FaultCategory1 category1)
            {
                DeleteLevel1Category(category1);
            }
            else if(_selectedNode.Tag is FaultCategory2 category2)
            {
                DeleteLevel2Category(category2);
            }
        }

        /// <summary>
        /// 新增一级分类
        /// </summary>
        private void AddLevel1Category()
        {
            try
            {
                // 生成新的ID
                var categories = FaultManager.GetInstance().GetAllCategory1();
                int newId = categories.Count > 0 ? categories.Max(c => c.FaultId) + 1 : 1;

                // 创建新的一级分类
                var newCategory = new FaultCategory1
                {
                    FaultId = newId,
                    FaultName = "Default",
                    Description = "",
                    SubCategories = new List<FaultCategory2>()
                };

                // 添加到数据中
                FaultManager.GetInstance().AddCategory1(newCategory);

                // 刷新树形结构
                RefreshTreeView();

                // 查找并选中新添加的节点
                TreeNode newNode = FindNodeByTag(treeViewCategory.Nodes[0], newCategory);
                if(newNode != null)
                {
                    treeViewCategory.SelectedNode = newNode;
                    _selectedNode = newNode;
                    LoadNodeDetails(newNode);

                    // 设置编辑模式并聚焦到名称输入框
                    _currentEditMode = EditMode.EditLevel1;
                    txtCategoryName.Focus();
                    txtCategoryName.SelectAll();
                }

                //UIMessageBox.ShowInfo("添加一级分类成功，请修改分类名称");
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加一级分类失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 新增二级分类
        /// </summary>
        private void AddLevel2Category(FaultCategory1 parentCategory)
        {
            try
            {
                // 生成新的ID
                var categories = FaultManager.GetInstance().GetCategory2ByParentId(parentCategory.FaultId);
                int newId = categories.Count > 0 ? categories.Max(c => c.FaultId) + 1 : 1;

                // 创建新的二级分类
                var newCategory = new FaultCategory2
                {
                    FaultId = newId,
                    ParentFaultId = parentCategory.FaultId,
                    FaultName = "Default",
                    Description = ""
                };

                // 添加到数据中
                FaultManager.GetInstance().AddCategory2(newCategory);

                // 刷新树形结构
                RefreshTreeView();

                // 查找并选中新添加的节点
                TreeNode parentNode = FindNodeByTag(treeViewCategory.Nodes[0], parentCategory);
                if(parentNode != null)
                {
                    TreeNode newNode = FindNodeByTag(parentNode, newCategory);
                    if(newNode != null)
                    {
                        treeViewCategory.SelectedNode = newNode;
                        _selectedNode = newNode;
                        LoadNodeDetails(newNode);

                        // 设置编辑模式并聚焦到名称输入框
                        _currentEditMode = EditMode.EditLevel2;
                        txtCategoryName.Focus();
                        txtCategoryName.SelectAll();
                    }
                }

                //UIMessageBox.ShowInfo("添加二级分类成功，请修改分类名称");
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加二级分类失败：{ex.Message}");
            }
        }



        /// <summary>
        /// 删除一级分类
        /// </summary>
        private void DeleteLevel1Category(FaultCategory1 category)
        {
            //if(category.SubCategories.Count > 0)
            //{
            //    UIMessageBox.ShowError("该分类下存在二级分类，无法删除");
            //    return;
            //}

            if(UIMessageBox.ShowAsk($"确定要删除分类 [{category.FaultName}] 吗？"))
            {
                FaultManager.GetInstance().DeleteCategory1(category.FaultId);

                // 清理选中状态
                _selectedNode = null;
                treeViewCategory.SelectedNode = null;

                RefreshTreeView();
                ClearDetailPanel();
                UpdateDetailPanelVisibility(null);
                //UIMessageBox.ShowInfo("删除一级分类成功");
            }
        }

        /// <summary>
        /// 删除二级分类
        /// </summary>
        private void DeleteLevel2Category(FaultCategory2 category)
        {
            if(UIMessageBox.ShowAsk($"确定要删除分类 [{category.FaultName}] 吗？"))
            {
                if(FaultManager.GetInstance().DeleteCategory2(category.FaultId))
                {
                    // 清理选中状态
                    _selectedNode = null;
                    treeViewCategory.SelectedNode = null;

                    RefreshTreeView();
                    ClearDetailPanel();
                    UpdateDetailPanelVisibility(null);
                    //UIMessageBox.ShowInfo("删除二级分类成功");
                }
            }
        }

        /// <summary>
        /// 保存当前编辑
        /// </summary>
        private bool SaveCurrentEdit()
        {
            if(_currentEditMode == EditMode.None || _selectedNode?.Tag == null)
                return true;

            try
            {
                if(string.IsNullOrWhiteSpace(txtCategoryName.Text))
                {
                    UIMessageBox.ShowError("故障名称不能为空！");
                    return false;
                }

                if(_currentEditMode == EditMode.EditLevel1 && _selectedNode.Tag is FaultCategory1 category1)
                {
                    category1.FaultName = txtCategoryName.Text.Trim();
                    category1.Description = txtDescription.Text.Trim();
                    FaultManager.GetInstance().UpdateCategory1(category1);
                }
                else if(_currentEditMode == EditMode.EditLevel2 && _selectedNode.Tag is FaultCategory2 category2)
                {
                    category2.FaultName = txtCategoryName.Text.Trim();
                    category2.Description = txtDescription.Text.Trim();
                    FaultManager.GetInstance().UpdateCategory2(category2);
                }

                RefreshTreeView();
                return true;
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成故障编号
        /// </summary>
        private string GenerateFaultId(FaultCategoryLevel level, int? parentId = null)
        {
            if(level == FaultCategoryLevel.Level1)
            {
                var categories = FaultManager.GetInstance().GetAllCategory1();
                int maxId = categories.Count > 0 ? categories.Max(c => c.FaultId) : 0;
                return (maxId + 1).ToString("D3");
            }
            else
            {
                if(parentId.HasValue)
                {
                    var categories = FaultManager.GetInstance().GetCategory2ByParentId(parentId.Value);
                    int maxId = categories.Count > 0 ? categories.Max(c => c.FaultId) : 0;
                    return $"{parentId:D3}-{maxId + 1:D3}";
                }
            }
            return "";
        }

        /// <summary>
        /// 根据Tag查找树节点
        /// </summary>
        private TreeNode FindNodeByTag(TreeNode parentNode, object tag)
        {
            if(parentNode.Tag == tag)
                return parentNode;

            foreach(TreeNode childNode in parentNode.Nodes)
            {
                TreeNode result = FindNodeByTag(childNode, tag);
                if(result != null)
                    return result;
            }

            return null;
        }

        #endregion
    }

    /// <summary>
    /// 编辑模式枚举
    /// </summary>
    public enum EditMode
    {
        /// <summary>
        /// 无编辑
        /// </summary>
        None,

        /// <summary>
        /// 编辑一级分类
        /// </summary>
        EditLevel1,

        /// <summary>
        /// 编辑二级分类
        /// </summary>
        EditLevel2
    }

    /// <summary>
    /// 故障分类级别枚举
    /// </summary>
    public enum FaultCategoryLevel
    {
        /// <summary>
        /// 一级分类
        /// </summary>
        Level1 = 1,

        /// <summary>
        /// 二级分类
        /// </summary>
        Level2 = 2
    }
}