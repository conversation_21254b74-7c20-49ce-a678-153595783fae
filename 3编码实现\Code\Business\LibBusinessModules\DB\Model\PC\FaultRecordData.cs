using LibBaseModules.DB;
using SqlSugar;
using System;
using System.ComponentModel;

namespace LibBusinessModules.DB.Models.PC
{
    /// <summary>
    /// 故障记录
    /// </summary>
    [SugarTable("fault_info")]
    [SugarIndex("index_sncode_time", nameof(SNCode), OrderByType.Asc, nameof(RecordTime), OrderByType.Desc)]
    [Description("故障记录")]
    public class FaultRecordData : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 主键ID（自增长）
        /// </summary>
        [SugarColumn(ColumnName = "id", ColumnDescription = "主键ID", IsPrimaryKey = true, IsIdentity = true)]
        [Description("主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 设备序列号（可选）
        /// </summary>
        [SugarColumn(ColumnName = "sncode", ColumnDescription = "设备序列号", Length = 50, IsNullable = true)]
        [Description("设备序列号")]
        public string SNCode { get; set; }

        /// <summary>
        /// 录入时间（自动生成当前时间）
        /// </summary>
        [SugarColumn(ColumnName = "recordtime", SerializeDateTimeFormat = "yyyy-MM-dd HH:mm:ss", ColumnDescription = "录入时间")]
        [Description("录入时间")]
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 类别1ID（必填）
        /// </summary>
        [SugarColumn(ColumnName = "category1_id", ColumnDescription = "类别1ID")]
        [Description("类别1ID")]
        public int Category1Id { get; set; }

        /// <summary>
        /// 类别2ID（必填）
        /// </summary>
        [SugarColumn(ColumnName = "category2_id", ColumnDescription = "类别2ID")]
        [Description("类别2ID")]
        public int Category2Id { get; set; }

        /// <summary>
        /// 问题描述（必填，支持多行文本）
        /// </summary>
        [SugarColumn(ColumnName = "problem_description", ColumnDescription = "问题描述", Length = 1000)]
        [Description("问题描述")]
        public string ProblemDescription { get; set; }

        #endregion

        #region 构造函数

        public FaultRecordData()
        {
            RecordTime = DateTime.Now;
        }

        #endregion
    }
}