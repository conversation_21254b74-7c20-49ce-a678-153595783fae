G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\LibBusinessModules.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\LibBusinessModules.pdb
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\CSkin.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\Newtonsoft.Json.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\LibBaseModules.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\LibBaseModules.pdb
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\SunnyUI.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\SunnyUI.Common.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\BouncyCastle.Cryptography.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\EntityFramework.SqlServer.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\EntityFramework.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\Google.Protobuf.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\Google.Protobuf.pdb
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\K4os.Compression.LZ4.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\K4os.Compression.LZ4.Streams.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\K4os.Hash.xxHash.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\MySql.Data.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\SqlSugar.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Data.SQLite.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Buffers.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Configuration.ConfigurationManager.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Data.SQLite.EF6.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Data.SQLite.Linq.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Diagnostics.DiagnosticSource.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.IO.Pipelines.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Memory.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Numerics.Vectors.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\System.Threading.Tasks.Extensions.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\bin\Debug\ZstdSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\x86\libSkiaSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\x64\libSkiaSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\arm64\libSkiaSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\libSkiaSharp.dylib
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\x86\libHarfBuzzSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\x64\libHarfBuzzSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\arm64\libHarfBuzzSharp.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\libHarfBuzzSharp.dylib
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\LibBusinessModules.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Product\Debug\LibBusinessModules.pdb
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.csproj.AssemblyReference.cache
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.UC_DBInfoManager.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.FrmDeviceConfig.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.UC_MeasureItemConfig.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.UC_DeviceManager.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.FaultManagement.UI.UC_FaultCategoryManager.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.UC_FTPInfoManager.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Properties.Resources.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.FrmEmployeeInfoConfig.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Config.UI.UC_EmployeeInfoManager.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Report.UI.UC_ReportDataShow.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Report.UI.UC_DeviceSelect.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_AlarmQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_CalibrationDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_DoubleCheckDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_ZeroCheckDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_SpanCheckDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_AddCheckDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_LightSourceInfoQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_DeviceInfoQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_ImnCalibrationDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_ImnMeasureDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_TnCalibrationDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_TnMeasureDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_MeasureDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_CurveDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Commom.FrmAboutMe.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Fault.UC_FaultStatistics.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.User.FrmChangePwd.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.SysConfig.FrmSystemConfig.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.FrmMain.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.User.FrmUserLogin.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Commom.FrmWelcome.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_DataQueryBase.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_LogQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataQuery.UC_AllDataQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Fault.UC_FaultRecordQuery.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Fault.FrmFaultAdd.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Commom.UC_DeviceSelector.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.Report.UI.UC_ReportExport.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Com.UC_ComTest.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.DataExtract.UC_DataExtract.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.UI.Report.UC_ReportCenter.resources
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.csproj.GenerateResource.cache
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.csproj.CoreCompileInputs.cache
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusin.532CBD9D.Up2Date
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.dll
G:\01-MyCode\00-mywork\02-faang\04-自动化测试软件\3编码实现\Code\Business\LibBusinessModules\obj\Debug\LibBusinessModules.pdb
