﻿using LibBusinessModules.Helper;
using LibBusinessModules.Report.UI;
using LibBusinessModules.UI.Com;
using LibBusinessModules.UI.Commom;
using LibBusinessModules.UI.DataExtract;
using LibBusinessModules.UI.DataQuery;
using LibBusinessModules.UI.Fault;
using LibBusinessModules.UI.Report;
using LibBusinessModules.UI.SysConfig;
using LibBusinessModules.UI.User;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI
{
    /// <summary>
    /// 主页面
    /// </summary>
    public partial class FrmMain : UIForm
    {
        #region 日志界面

        /// <summary>
        /// 报表中心页面
        /// </summary>
        private UC_ReportCenter _ucReportCenter = new UC_ReportCenter();

        /// <summary>
        /// 数据提取页面
        /// </summary>
        private UC_DataExtract _ucDataExtract = new UC_DataExtract();

        /// <summary>
        /// 报告导出页面
        /// </summary>
        private UC_ReportExport _ucReportExport = new UC_ReportExport();

        /// <summary>
        /// 故障统计页面
        /// </summary>
        private UC_FaultStatistics _ucFaultStatistics = new UC_FaultStatistics();

        /// <summary>
        /// 故障记录管理页面
        /// </summary>
        private UC_FaultRecordQuery _ucFaultRecordQuery = new UC_FaultRecordQuery();

        /// <summary>
        /// 数据查询页面
        /// </summary>
        private UC_AllDataQuery _ucAllDataQuery = new UC_AllDataQuery();

        /// <summary>
        /// 通信测试页面
        /// </summary>
        private UC_ComTest _ucComTest = new UC_ComTest();

        #endregion

        #region 构造

        public FrmMain()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 加载初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmMain_Load(object sender, EventArgs e)
        {
            // 开发模式下不显示
            if(!DesignMode)
            {
                this.WindowState = FormWindowState.Minimized;

                // 欢迎界面，加载默认信息
                new FrmWelcome().ShowDialog();

                // 设置默认显示的页面
                SetCurrentUC(_ucReportCenter);

                this.WindowState = FormWindowState.Maximized;

#if DEBUG
                // Debug 模式下自动登录
                GlobalHelper.IsUserLogin = true;
#endif

                UpdateBtnVisible();
            }
        }

        #region 用户管理

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUserLogin_Click(object sender, EventArgs e)
        {
            // 当前用户未登录
            if(!GlobalHelper.IsUserLogin)
            {
                // 确认登录
                if(new FrmUserLogin().ShowDialog() == DialogResult.OK)
                {
                    GlobalHelper.IsUserLogin = true;
                    UpdateBtnVisible();
                }
            }
        }

        /// <summary>
        /// 用户注销
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUserLogout_Click(object sender, EventArgs e)
        {
            // 当前用户已登录
            if(GlobalHelper.IsUserLogin)
            {
                // 确认注销
                if(UIMessageBox.ShowAsk("确认注销当前用户?"))
                {
                    GlobalHelper.IsUserLogin = false;
                    UpdateBtnVisible();
                }
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnChangePwd_Click(object sender, EventArgs e)
        {
            new FrmChangePwd().ShowDialog();
        }

        #endregion

        #region 数据相关

        /// <summary>
        /// 报表中心
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReport_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucReportCenter);
        }


        /// <summary>
        /// 故障统计
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnFaultStatistics_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucFaultStatistics);
        }

        /// <summary>
        /// 故障记录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnFaultQuery_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucFaultRecordQuery);
        }

        /// <summary>
        /// 数据提取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDataExtract_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucDataExtract);
        }

        /// <summary>
        /// 报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReportExport_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucReportExport);
        }

        /// <summary>
        /// 数据查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDataQuery_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucAllDataQuery);
        }

        #endregion

        /// <summary>
        /// 系统配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSysConfig_Click(object sender, EventArgs e)
        {
            new FrmSystemConfig().ShowDialog();
        }

        /// <summary>
        /// 通信测试
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnComTest_Click(object sender, EventArgs e)
        {
            SetCurrentUC(_ucComTest);
        }

        /// <summary>
        /// 版本信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSoftInfo_Click(object sender, EventArgs e)
        {
            new FrmAboutMe().ShowDialog();
        }

        /// <summary>
        /// 定时刷新数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timerSystemDate_Tick(object sender, EventArgs e)
        {
            try
            {
                lblSystemDate.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            }
            catch(Exception ex)
            {
                // ignored
            }
        }

        /// <summary>
        /// 软件退出确认
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 非强制重启时，确认是否退出
            if(GlobalHelper.IsForseClose != true)
            {
                e.Cancel = !UIMessageBox.ShowAsk("确定退出本系统?");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置当前显示的页面
        /// </summary>
        /// <param name="uc"></param>
        private void SetCurrentUC(UIUserControl uc)
        {
            if(!pnlMain.Controls.Contains(uc))
            {
                pnlMain.Controls.Clear();
                uc.Dock = DockStyle.Fill;
                pnlMain.Controls.Add(uc);
            }
        }

        /// <summary>
        /// 更新控件可见性
        /// </summary>
        private void UpdateBtnVisible()
        {
            btnUserLogout.Visible = btnChangePwd.Visible = btnDataExtract.Visible = btnReportExport.Visible = btnFaultRecord.Visible = btnDataQuery.Visible = btnSysConfig.Visible = btnComTest.Visible = GlobalHelper.IsUserLogin;

            btnUserLogin.Visible = !GlobalHelper.IsUserLogin;
        }

        #endregion
    }
}