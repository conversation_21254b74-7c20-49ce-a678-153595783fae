{"RootPath": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules", "ProjectFileName": "LibBusinessModules.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Config\\DBInfo\\UI\\UC\\UC_DBInfoManager.cs"}, {"SourceFile": "Config\\DBInfo\\UI\\UC\\UC_DBInfoManager.Designer.cs"}, {"SourceFile": "Config\\EmployeeInfo\\EmployeeInfo.cs"}, {"SourceFile": "Config\\DBInfo\\DBInfo.cs"}, {"SourceFile": "Config\\FaultManagement\\FaultManager.cs"}, {"SourceFile": "Config\\FaultManagement\\Model\\Models.cs"}, {"SourceFile": "Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs"}, {"SourceFile": "Config\\FaultManagement\\UI\\UC_FaultCategoryManager.Designer.cs"}, {"SourceFile": "Config\\FTPInfo\\FTPInfo.cs"}, {"SourceFile": "Config\\FTPInfo\\UI\\UC\\UC_FTPInfoManager.cs"}, {"SourceFile": "Config\\FTPInfo\\UI\\UC\\UC_FTPInfoManager.Designer.cs"}, {"SourceFile": "DB\\Fault\\Model\\FaultCategory.cs"}, {"SourceFile": "DB\\Model\\Device\\RawAddCheckData.cs"}, {"SourceFile": "DB\\Model\\Device\\RawAlarmData.cs"}, {"SourceFile": "DB\\Model\\Device\\RawDoubleCheckData.cs"}, {"SourceFile": "DB\\Model\\Device\\RawOperData.cs"}, {"SourceFile": "DB\\Model\\Device\\RawSpanCheckData.cs"}, {"SourceFile": "DB\\Model\\Device\\RawZeroCheckData.cs"}, {"SourceFile": "DB\\Model\\PC\\OperData.cs"}, {"SourceFile": "DB\\Model\\PC\\AlarmData.cs"}, {"SourceFile": "Config\\Device\\DeviceConfig.cs"}, {"SourceFile": "Config\\Device\\DeviceManager.cs"}, {"SourceFile": "DB\\Helper\\DBHelper.cs"}, {"SourceFile": "DB\\Model\\Device\\RawAdjData.cs"}, {"SourceFile": "DB\\Model\\Device\\AdjSignalInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\DeviceSNInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\FlowVersionInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\HmiVersionInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\ImnRawAdjdata.cs"}, {"SourceFile": "DB\\Model\\Device\\ImnRawMeasuredata.cs"}, {"SourceFile": "DB\\Model\\Device\\LineData.cs"}, {"SourceFile": "DB\\Model\\Device\\MainVersionInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\RawMeasureData.cs"}, {"SourceFile": "DB\\Model\\Device\\PumpSNInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\PumpVersionInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\TnRawAdjdata.cs"}, {"SourceFile": "DB\\Model\\Device\\TnRawMeasuredata.cs"}, {"SourceFile": "DB\\Model\\Device\\ValveSNInfo.cs"}, {"SourceFile": "DB\\Model\\Device\\ValveVersionInfo.cs"}, {"SourceFile": "DB\\Model\\PC\\LightSourceInfo.cs"}, {"SourceFile": "DB\\Model\\PC\\DeviceInfo.cs"}, {"SourceFile": "DB\\Model\\PC\\CalibrationData.cs"}, {"SourceFile": "DB\\Model\\PC\\ImnCalibrationData.cs"}, {"SourceFile": "DB\\Model\\PC\\ImnMeasureData.cs"}, {"SourceFile": "DB\\Model\\PC\\CurveData.cs"}, {"SourceFile": "DB\\Model\\PC\\MeasureData.cs"}, {"SourceFile": "DB\\Model\\PC\\AddCheckData.cs"}, {"SourceFile": "DB\\Model\\PC\\DoubleCheckData.cs"}, {"SourceFile": "DB\\Model\\PC\\SpanCheckData.cs"}, {"SourceFile": "DB\\Model\\PC\\ZeroCheckData.cs"}, {"SourceFile": "DB\\Model\\PC\\TnCalibrationData.cs"}, {"SourceFile": "DB\\Model\\PC\\TnMeasureData.cs"}, {"SourceFile": "DB\\Model\\PC\\FaultRecordData.cs"}, {"SourceFile": "DB\\Model\\OneDeviceAllInfo.cs"}, {"SourceFile": "Config\\SystemConfig.cs"}, {"SourceFile": "Config\\Device\\UI\\FrmDeviceConfig.cs"}, {"SourceFile": "Config\\Device\\UI\\FrmDeviceConfig.Designer.cs"}, {"SourceFile": "Config\\Device\\UI\\UC\\UC_MeasureItemConfig.cs"}, {"SourceFile": "Config\\Device\\UI\\UC\\UC_MeasureItemConfig.Designer.cs"}, {"SourceFile": "Config\\Device\\UI\\UC\\UC_DeviceManager.cs"}, {"SourceFile": "Config\\Device\\UI\\UC\\UC_DeviceManager.Designer.cs"}, {"SourceFile": "Helper\\AppInfoHelper.cs"}, {"SourceFile": "Helper\\GlobalHelper.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Config\\EmployeeInfo\\UI\\FrmEmployeeInfoConfig.cs"}, {"SourceFile": "Config\\EmployeeInfo\\UI\\FrmEmployeeInfoConfig.Designer.cs"}, {"SourceFile": "Config\\EmployeeInfo\\UI\\UC\\UC_EmployeeInfoManager.cs"}, {"SourceFile": "Config\\EmployeeInfo\\UI\\UC\\UC_EmployeeInfoManager.Designer.cs"}, {"SourceFile": "Report\\Config\\DeviceRawReportData.cs"}, {"SourceFile": "Report\\Config\\ReportCalculateNode.cs"}, {"SourceFile": "Report\\Config\\ReportCalibrationData.cs"}, {"SourceFile": "Report\\Config\\ReportCurveData.cs"}, {"SourceFile": "Report\\Config\\ReportMeasureData.cs"}, {"SourceFile": "Report\\Helper\\DataRecordExportHelper.cs"}, {"SourceFile": "Report\\Helper\\InspectionRecordExportHelper.cs"}, {"SourceFile": "Report\\Helper\\CertificateExportHelper.cs"}, {"SourceFile": "Report\\Helper\\ReportDocumentHelper.cs"}, {"SourceFile": "Report\\Helper\\ReportPathHelper.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_ReportDataShow.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_ReportDataShow.Designer.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_DeviceSelect.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_DeviceSelect.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_AlarmQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_AlarmQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_CalibrationDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_CalibrationDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_DoubleCheckDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_DoubleCheckDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ZeroCheckDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ZeroCheckDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_SpanCheckDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_SpanCheckDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_AddCheckDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_AddCheckDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_LightSourceInfoQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_LightSourceInfoQuery.designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_DeviceInfoQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_DeviceInfoQuery.designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ImnCalibrationDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ImnCalibrationDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ImnMeasureDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_ImnMeasureDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_TnCalibrationDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_TnCalibrationDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_TnMeasureDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_TnMeasureDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_MeasureDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_MeasureDataQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_CurveDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_CurveDataQuery.Designer.cs"}, {"SourceFile": "UI\\Commom\\FrmAboutMe.cs"}, {"SourceFile": "UI\\Commom\\FrmAboutMe.Designer.cs"}, {"SourceFile": "UI\\User\\FrmChangePwd.cs"}, {"SourceFile": "UI\\User\\FrmChangePwd.Designer.cs"}, {"SourceFile": "UI\\SystemConfig\\FrmSystemConfig.cs"}, {"SourceFile": "UI\\SystemConfig\\FrmSystemConfig.Designer.cs"}, {"SourceFile": "UI\\FrmMain.cs"}, {"SourceFile": "UI\\FrmMain.Designer.cs"}, {"SourceFile": "UI\\User\\FrmUserLogin.cs"}, {"SourceFile": "UI\\User\\FrmUserLogin.Designer.cs"}, {"SourceFile": "UI\\Commom\\FrmWelcome.cs"}, {"SourceFile": "UI\\Commom\\FrmWelcome.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\UC_DataQueryBase.cs"}, {"SourceFile": "UI\\DataQuery\\UC_DataQueryBase.designer.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_LogQuery.cs"}, {"SourceFile": "UI\\DataQuery\\Sub\\UC_LogQuery.Designer.cs"}, {"SourceFile": "UI\\DataQuery\\UC_AllDataQuery.cs"}, {"SourceFile": "UI\\DataQuery\\UC_AllDataQuery.Designer.cs"}, {"SourceFile": "UI\\Fault\\UC_FaultManager.cs"}, {"SourceFile": "UI\\Fault\\UC_FaultManager.Designer.cs"}, {"SourceFile": "UI\\Fault\\FrmFaultAdd.cs"}, {"SourceFile": "UI\\Fault\\FrmFaultAdd.Designer.cs"}, {"SourceFile": "UI\\Commom\\UC_DeviceSelector.cs"}, {"SourceFile": "UI\\Commom\\UC_DeviceSelector.Designer.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_ReportExport.cs"}, {"SourceFile": "Report\\UI\\UC\\UC_ReportExport.Designer.cs"}, {"SourceFile": "UI\\Com\\UC_ComTest.cs"}, {"SourceFile": "UI\\Com\\UC_ComTest.Designer.cs"}, {"SourceFile": "UI\\DataExtract\\UC_DataExtract.cs"}, {"SourceFile": "UI\\DataExtract\\UC_DataExtract.Designer.cs"}, {"SourceFile": "UI\\Report\\UC_ReportCenter.cs"}, {"SourceFile": "UI\\Report\\UC_ReportCenter.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "F:\\Nuget\\packages\\bouncycastle.cryptography\\2.3.1\\lib\\net461\\BouncyCastle.Cryptography.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\entityframework\\6.4.4\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\entityframework\\6.4.4\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\enums.net\\4.0.1\\lib\\net45\\Enums.NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\extendednumerics.bigdecimal\\2025.1001.2.129\\lib\\net48\\ExtendedNumerics.BigDecimal.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\google.protobuf\\3.26.1\\lib\\net45\\Google.Protobuf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sharpziplib\\1.4.2\\lib\\netstandard2.0\\ICSharpCode.SharpZipLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\k4os.compression.lz4\\1.3.8\\lib\\net462\\K4os.Compression.LZ4.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\k4os.compression.lz4.streams\\1.3.8\\lib\\net462\\K4os.Compression.LZ4.Streams.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\k4os.hash.xxhash\\1.0.8\\lib\\net462\\K4os.Hash.xxHash.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Product\\Debug\\LibBaseModules.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Product\\Debug\\LibBaseModules.dll"}, {"Reference": "F:\\Nuget\\packages\\mathnet.numerics.signed\\5.0.0\\lib\\net48\\MathNet.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\microsoft.bcl.asyncinterfaces\\5.0.0\\lib\\net461\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.0\\lib\\netstandard2.0\\Microsoft.IO.RecyclableMemoryStream.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\mysql.data\\9.2.0\\lib\\net48\\MySql.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\newtonsoft.json\\13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\npoi\\2.7.3\\lib\\net472\\NPOI.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\npoi\\2.7.3\\lib\\net472\\NPOI.OOXML.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\npoi\\2.7.3\\lib\\net472\\NPOI.OpenXml4Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\npoi\\2.7.3\\lib\\net472\\NPOI.OpenXmlFormats.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sixlabors.fonts\\1.0.1\\lib\\netstandard2.0\\SixLabors.Fonts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sixlabors.imagesharp\\2.1.10\\lib\\net472\\SixLabors.ImageSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sqlsugar\\5.1.4.187\\lib\\SqlSugar.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sunnyui.common\\3.8.2\\lib\\net472\\SunnyUI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\sunnyui\\3.8.2\\lib\\net472\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.buffers\\4.5.1\\ref\\net45\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.configuration.configurationmanager\\8.0.0\\lib\\net462\\System.Configuration.ConfigurationManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\stub.system.data.sqlite.core.netframework\\1.0.119\\lib\\net46\\System.Data.SQLite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.data.sqlite.ef6\\1.0.119\\lib\\net46\\System.Data.SQLite.EF6.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.data.sqlite.linq\\1.0.119\\lib\\net46\\System.Data.SQLite.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.1\\lib\\net462\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.io.pipelines\\5.0.2\\lib\\net461\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.memory\\4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.numerics.vectors\\4.5.0\\ref\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\lib\\net462\\System.Security.Cryptography.Pkcs.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.security.cryptography.xml\\8.0.2\\lib\\net462\\System.Security.Cryptography.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.text.encoding.codepages\\5.0.0\\lib\\net461\\System.Text.Encoding.CodePages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "F:\\Nuget\\packages\\zstdsharp.port\\0.8.0\\lib\\net462\\ZstdSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Product\\Debug\\LibBusinessModules.dll", "OutputItemRelativePath": "LibBusinessModules.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}