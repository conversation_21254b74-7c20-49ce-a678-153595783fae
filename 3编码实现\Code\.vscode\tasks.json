{"version": "2.0.0", "tasks": [{"label": "restore", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/IAutoTestX.sln", "/t:restore"], "problemMatcher": "$msCompile"}, {"label": "build", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/IAutoTestX.sln", "/p:Configuration=Debug", "/p:Platform=Any CPU", "/t:build", "/m", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "dependsOn": ["restore"]}, {"label": "rebuild", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/IAutoTestX.sln", "/p:Configuration=Debug", "/p:Platform=Any CPU", "/t:rebuild", "/m", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}]}