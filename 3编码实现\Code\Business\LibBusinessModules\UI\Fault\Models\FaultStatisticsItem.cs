using System.ComponentModel;

namespace LibBusinessModules.UI.Fault.Models
{
    /// <summary>
    /// 故障统计项数据模型
    /// </summary>
    public class FaultStatisticsItem
    {
        /// <summary>
        /// 统计类型
        /// </summary>
        [Description("统计类型")]
        public string StatisticsType { get; set; }

        /// <summary>
        /// 统计项名称
        /// </summary>
        [Description("统计项名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 故障数量
        /// </summary>
        [Description("故障数量")]
        public int FaultCount { get; set; }

        /// <summary>
        /// 测试仪表总数量
        /// </summary>
        [Description("测试仪表总数量")]
        public int TotalDeviceCount { get; set; }

        /// <summary>
        /// 故障率（百分比）
        /// </summary>
        [Description("故障率")]
        public double FaultRate { get; set; }

        /// <summary>
        /// 故障率显示文本
        /// </summary>
        [Description("故障率显示")]
        public string FaultRateDisplay => $"{FaultRate:F2}%";
    }

    /// <summary>
    /// 故障统计汇总数据
    /// </summary>
    public class FaultStatisticsSummary
    {
        /// <summary>
        /// 查询时间段
        /// </summary>
        [Description("查询时间段")]
        public string QueryPeriod { get; set; }

        /// <summary>
        /// 总测试仪表数量
        /// </summary>
        [Description("总测试仪表数量")]
        public int TotalDeviceCount { get; set; }

        /// <summary>
        /// 故障仪表数量
        /// </summary>
        [Description("故障仪表数量")]
        public int FaultDeviceCount { get; set; }

        /// <summary>
        /// 总故障记录数量
        /// </summary>
        [Description("总故障记录数量")]
        public int TotalFaultRecordCount { get; set; }

        /// <summary>
        /// 总故障率
        /// </summary>
        [Description("总故障率")]
        public double TotalFaultRate { get; set; }

        /// <summary>
        /// 总故障率显示文本
        /// </summary>
        [Description("总故障率显示")]
        public string TotalFaultRateDisplay => $"{TotalFaultRate:F2}%";
    }
}
