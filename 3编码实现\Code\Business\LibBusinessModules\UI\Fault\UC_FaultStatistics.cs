using Sunny.UI;
using System;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障统计界面
    /// </summary>
    public partial class UC_FaultStatistics : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        #endregion

        #region 构造函数

        public UC_FaultStatistics()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
            }
            catch(Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError(ex.Message);
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            FillDataToDgv();
        }

        /// <summary>
        /// 填充数据到DataGridView
        /// </summary>
        private void FillDataToDgv()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据查询中，请稍候...");

            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
                UIFormServiceHelper.HideStatusForm(this.ParentForm);
            }
        }

        #endregion
    }
}