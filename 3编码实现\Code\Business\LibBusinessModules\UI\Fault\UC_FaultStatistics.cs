using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.DB;
using LibBusinessModules.Config;
using LibBusinessModules.Config.FaultManagement.Models;
using LibBusinessModules.UI.Fault.Models;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障统计界面
    /// </summary>
    public partial class UC_FaultStatistics : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据表格
        /// </summary>
        private UIDataGridView dgvStatistics;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<FaultStatisticsItem> _statisticsData;

        #endregion

        #region 构造函数

        public UC_FaultStatistics()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出图片，请稍候...");

                ExportToPNG();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 导出统计结果为PNG图片
        /// </summary>
        private void ExportToPNG()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowWarning("没有统计数据可以导出！");
                return;
            }

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 创建一个临时的Panel来包含要导出的内容
                    var exportPanel = CreateExportPanel();

                    // 将Panel内容导出为图片
                    using (var bitmap = new Bitmap(exportPanel.Width, exportPanel.Height))
                    {
                        exportPanel.DrawToBitmap(bitmap, new Rectangle(0, 0, exportPanel.Width, exportPanel.Height));
                        bitmap.Save(saveFileDialog.FileName, System.Drawing.Imaging.ImageFormat.Png);
                    }

                    // 清理临时Panel
                    exportPanel.Dispose();

                    UIMessageBox.ShowSuccess($"统计结果已成功导出到：{saveFileDialog.FileName}");
                }
                catch (Exception ex)
                {
                    throw new Exception($"保存图片文件失败：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 创建用于导出的Panel
        /// </summary>
        /// <returns>包含统计内容的Panel</returns>
        private Panel CreateExportPanel()
        {
            var panel = new Panel
            {
                Size = new Size(1000, 600),
                BackColor = Color.White
            };

            // 添加标题
            var titleLabel = new Label
            {
                Text = $"故障统计报告 ({StartTime:yyyy-MM-dd} 至 {EndTime:yyyy-MM-dd})",
                Font = new Font("微软雅黑", 16, FontStyle.Bold),
                Size = new Size(1000, 40),
                Location = new Point(0, 10),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.White
            };
            panel.Controls.Add(titleLabel);

            // 创建统计表格的副本
            var exportDataGridView = new DataGridView
            {
                Location = new Point(50, 60),
                Size = new Size(900, 500),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                GridColor = Color.LightGray,
                Font = new Font("微软雅黑", 10),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.LightBlue,
                    Font = new Font("微软雅黑", 10, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    SelectionBackColor = Color.LightBlue,
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };

            // 复制列结构
            exportDataGridView.Columns.Add("StatisticsType", "统计类型");
            exportDataGridView.Columns.Add("ItemName", "统计项名称");
            exportDataGridView.Columns.Add("FaultCount", "故障数量");
            exportDataGridView.Columns.Add("TotalDeviceCount", "测试仪表总数");
            exportDataGridView.Columns.Add("FaultRate", "故障率");

            // 设置列宽
            exportDataGridView.Columns["StatisticsType"].Width = 120;
            exportDataGridView.Columns["ItemName"].Width = 250;
            exportDataGridView.Columns["FaultCount"].Width = 120;
            exportDataGridView.Columns["TotalDeviceCount"].Width = 150;
            exportDataGridView.Columns["FaultRate"].Width = 120;

            // 复制数据
            foreach (var item in _statisticsData)
            {
                exportDataGridView.Rows.Add(
                    item.StatisticsType,
                    item.ItemName,
                    item.FaultCount,
                    item.TotalDeviceCount,
                    item.FaultRateDisplay
                );
            }

            panel.Controls.Add(exportDataGridView);
            return panel;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 创建统计结果表格
            dgvStatistics = new UIDataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // 设置表格列
            SetupDataGridViewColumns();

            // 添加到主面板
            pnlMain.Controls.Add(dgvStatistics);
        }

        /// <summary>
        /// 设置数据表格列
        /// </summary>
        private void SetupDataGridViewColumns()
        {
            dgvStatistics.Columns.Clear();
            dgvStatistics.Columns.Add("StatisticsType", "统计类型");
            dgvStatistics.Columns.Add("ItemName", "统计项名称");
            dgvStatistics.Columns.Add("FaultCount", "故障数量");
            dgvStatistics.Columns.Add("TotalDeviceCount", "测试仪表总数");
            dgvStatistics.Columns.Add("FaultRate", "故障率");

            // 设置列宽比例
            dgvStatistics.Columns["StatisticsType"].FillWeight = 15;
            dgvStatistics.Columns["ItemName"].FillWeight = 25;
            dgvStatistics.Columns["FaultCount"].FillWeight = 15;
            dgvStatistics.Columns["TotalDeviceCount"].FillWeight = 20;
            dgvStatistics.Columns["FaultRate"].FillWeight = 15;

            // 设置数值列居中对齐
            dgvStatistics.Columns["FaultCount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvStatistics.Columns["TotalDeviceCount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvStatistics.Columns["FaultRate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            CalculateAndDisplayStatistics();
        }

        /// <summary>
        /// 计算并显示统计结果
        /// </summary>
        private void CalculateAndDisplayStatistics()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                _statisticsData = CalculateFaultStatistics();

                // 显示统计结果
                DisplayStatisticsResults();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"统计计算失败：{ex.Message}");
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 计算故障统计数据
        /// </summary>
        /// <returns>统计结果列表</returns>
        private List<FaultStatisticsItem> CalculateFaultStatistics()
        {
            var statisticsItems = new List<FaultStatisticsItem>();

            // 获取指定时间段内的故障记录
            var faultRecords = GetFaultRecordsInPeriod();

            // 获取所有设备信息
            var allDevices = GetAllDevices();

            // 计算按类别1统计的故障率
            var category1Statistics = CalculateCategory1Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category1Statistics);

            // 计算按类别2统计的故障率
            var category2Statistics = CalculateCategory2Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category2Statistics);

            // 计算按因子统计的故障率
            var factorStatistics = CalculateFactorStatistics(faultRecords, allDevices);
            statisticsItems.AddRange(factorStatistics);

            // 计算总故障率
            var totalStatistics = CalculateTotalStatistics(faultRecords, allDevices);
            statisticsItems.Add(totalStatistics);

            return statisticsItems;
        }

        /// <summary>
        /// 获取指定时间段内的故障记录
        /// </summary>
        /// <returns>故障记录列表</returns>
        private List<FaultRecordData> GetFaultRecordsInPeriod()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<FaultRecordData>()
                     .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime)
                     .ToList();
        }

        /// <summary>
        /// 获取所有设备信息
        /// </summary>
        /// <returns>设备信息列表</returns>
        private List<DeviceInfo> GetAllDevices()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<DeviceInfo>().ToList();
        }

        /// <summary>
        /// 计算按类别1统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别1统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory1Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                var faultCount = faultRecords.Count(f => f.Category1Id == category1.FaultId);
                var totalDeviceCount = allDevices.Count;
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别1",
                    ItemName = category1.FaultName,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics;
        }

        /// <summary>
        /// 计算按类别2统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别2统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory2Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                foreach (var category2 in category1.SubCategories)
                {
                    var faultCount = faultRecords.Count(f => f.Category2Id == category2.FaultId);
                    var totalDeviceCount = allDevices.Count;
                    var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                    statistics.Add(new FaultStatisticsItem
                    {
                        StatisticsType = "类别2",
                        ItemName = $"{category1.FaultName}-{category2.FaultName}",
                        FaultCount = faultCount,
                        TotalDeviceCount = totalDeviceCount,
                        FaultRate = faultRate
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// 计算按因子统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>因子统计结果</returns>
        private List<FaultStatisticsItem> CalculateFactorStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();

            // 按因子分组统计
            var factorGroups = new Dictionary<string, List<string>>();
            var factorDeviceCounts = new Dictionary<string, int>();

            // 遍历所有设备，按因子分组
            foreach (var device in allDevices)
            {
                try
                {
                    var factor = GetFactorFromSNCode(device.SNCode);
                    if (!factorGroups.ContainsKey(factor))
                    {
                        factorGroups[factor] = new List<string>();
                        factorDeviceCounts[factor] = 0;
                    }
                    factorGroups[factor].Add(device.SNCode);
                    factorDeviceCounts[factor]++;
                }
                catch
                {
                    // 忽略获取因子失败的设备
                }
            }

            // 计算每个因子的故障率
            foreach (var factorGroup in factorGroups)
            {
                var factor = factorGroup.Key;
                var deviceSNCodes = factorGroup.Value;
                var totalDeviceCount = factorDeviceCounts[factor];

                // 统计该因子下的故障数量
                var faultCount = faultRecords.Count(f => deviceSNCodes.Contains(f.SNCode));
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "因子",
                    ItemName = factor,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics.OrderBy(s => s.ItemName).ToList();
        }

        /// <summary>
        /// 计算总故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>总故障率统计结果</returns>
        private FaultStatisticsItem CalculateTotalStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var totalDeviceCount = allDevices.Count;

            // 统计有故障的设备数量（去重）
            var faultDeviceSNCodes = faultRecords.Select(f => f.SNCode).Distinct().ToList();
            var faultDeviceCount = faultDeviceSNCodes.Count;

            var totalFaultRate = totalDeviceCount > 0 ? (double)faultDeviceCount / totalDeviceCount * 100 : 0;

            return new FaultStatisticsItem
            {
                StatisticsType = "总计",
                ItemName = "总故障率",
                FaultCount = faultDeviceCount,
                TotalDeviceCount = totalDeviceCount,
                FaultRate = totalFaultRate
            };
        }

        /// <summary>
        /// 根据设备序列号获取因子信息
        /// </summary>
        /// <param name="snCode">设备序列号</param>
        /// <returns>因子信息</returns>
        private string GetFactorFromSNCode(string snCode)
        {
            if (string.IsNullOrEmpty(snCode))
                return "未知";

            try
            {
                // 根据序列号前6位获取设备配置
                if (snCode.Length >= 6)
                {
                    string idCode = snCode.Substring(0, 6);
                    var deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(idCode);
                    if (deviceConfig != null)
                    {
                        return deviceConfig.Factor;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不影响显示
            }

            return "未知";
        }

        /// <summary>
        /// 显示统计结果
        /// </summary>
        private void DisplayStatisticsResults()
        {
            dgvStatistics.Rows.Clear();

            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowInfo("指定时间段内没有故障数据。");
                return;
            }

            foreach (var item in _statisticsData)
            {
                int rowIndex = dgvStatistics.Rows.Add();
                DataGridViewRow row = dgvStatistics.Rows[rowIndex];

                row.Cells["StatisticsType"].Value = item.StatisticsType;
                row.Cells["ItemName"].Value = item.ItemName;
                row.Cells["FaultCount"].Value = item.FaultCount;
                row.Cells["TotalDeviceCount"].Value = item.TotalDeviceCount;
                row.Cells["FaultRate"].Value = item.FaultRateDisplay;

                row.Tag = item;
            }
        }

        #endregion
    }
}