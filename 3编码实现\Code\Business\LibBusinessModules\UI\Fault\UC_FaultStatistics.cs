﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.DB;
using LibBusinessModules.Config;
using LibBusinessModules.Config.FaultManagement.Models;
using LibBusinessModules.UI.Fault.Models;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障统计界面
    /// </summary>
    public partial class UC_FaultStatistics : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<FaultStatisticsItem> _statisticsData;

        /// <summary>
        /// 类别1图表控件
        /// </summary>
        private Panel category1Chart;

        /// <summary>
        /// 类别2图表控件
        /// </summary>
        private Panel category2Chart;

        /// <summary>
        /// 因子图表控件
        /// </summary>
        private Panel factorChart;

        /// <summary>
        /// 总故障率图表控件
        /// </summary>
        private Panel totalChart;

        #endregion

        #region 构造函数

        public UC_FaultStatistics()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出图片，请稍候...");

                ExportToPNG();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        /// <summary>
        /// 导出统计结果为PNG图片
        /// </summary>
        private void ExportToPNG()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowWarning("没有统计数据可以导出！");
                return;
            }

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 创建一个临时的Panel来包含要导出的内容
                    var exportPanel = CreateExportPanel();

                    // 将Panel内容导出为图片
                    using (var bitmap = new Bitmap(exportPanel.Width, exportPanel.Height))
                    {
                        exportPanel.DrawToBitmap(bitmap, new Rectangle(0, 0, exportPanel.Width, exportPanel.Height));
                        bitmap.Save(saveFileDialog.FileName, System.Drawing.Imaging.ImageFormat.Png);
                    }

                    // 清理临时Panel
                    exportPanel.Dispose();

                    UIMessageBox.ShowSuccess($"统计结果已成功导出到：{saveFileDialog.FileName}");
                }
                catch (Exception ex)
                {
                    throw new Exception($"保存图片文件失败：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 创建用于导出的Panel
        /// </summary>
        /// <returns>包含统计内容的Panel</returns>
        private Panel CreateExportPanel()
        {
            var panel = new Panel
            {
                Size = new Size(1200, 800),
                BackColor = Color.White
            };

            // 添加标题
            var titleLabel = new Label
            {
                Text = $"故障统计报告 ({StartTime:yyyy-MM-dd} 至 {EndTime:yyyy-MM-dd})",
                Font = new Font("微软雅黑", 16, FontStyle.Bold),
                Size = new Size(1200, 40),
                Location = new Point(0, 10),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.White
            };
            panel.Controls.Add(titleLabel);

            // 创建图表区域的副本
            var chartsPanel = new Panel
            {
                Location = new Point(10, 60),
                Size = new Size(1180, 720),
                BackColor = Color.White
            };

            // 创建四个图表区域
            var category1Panel = CreateExportChartPanel("按类别1统计故障率", new Point(0, 0), new Size(580, 350));
            var category2Panel = CreateExportChartPanel("按类别2统计故障率", new Point(590, 0), new Size(580, 350));
            var factorPanel = CreateExportChartPanel("按因子统计故障率", new Point(0, 360), new Size(580, 350));
            var totalPanel = CreateExportChartPanel("总故障率统计", new Point(590, 360), new Size(580, 350));

            // 绘制图表内容
            category1Panel.Paint += (s, e) => {
                var data = _statisticsData?.Where(x => x.StatisticsType == "类别1").ToList();
                if (data != null && data.Count > 0)
                    DrawBarChart(e.Graphics, data, category1Panel.ClientRectangle);
            };

            category2Panel.Paint += (s, e) => {
                var data = _statisticsData?.Where(x => x.StatisticsType == "类别2").ToList();
                if (data != null && data.Count > 0)
                    DrawBarChart(e.Graphics, data, category2Panel.ClientRectangle);
            };

            factorPanel.Paint += (s, e) => {
                var data = _statisticsData?.Where(x => x.StatisticsType == "因子").ToList();
                if (data != null && data.Count > 0)
                    DrawBarChart(e.Graphics, data, factorPanel.ClientRectangle);
            };

            totalPanel.Paint += (s, e) => {
                var data = _statisticsData?.Where(x => x.StatisticsType == "总计").FirstOrDefault();
                if (data != null)
                    DrawPieChart(e.Graphics, data, totalPanel.ClientRectangle);
            };

            chartsPanel.Controls.Add(category1Panel);
            chartsPanel.Controls.Add(category2Panel);
            chartsPanel.Controls.Add(factorPanel);
            chartsPanel.Controls.Add(totalPanel);

            panel.Controls.Add(chartsPanel);
            return panel;
        }

        /// <summary>
        /// 创建导出用的图表面板
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="location">位置</param>
        /// <param name="size">大小</param>
        /// <returns>图表面板</returns>
        private Panel CreateExportChartPanel(string title, Point location, Size size)
        {
            var panel = new Panel
            {
                Location = location,
                Size = size,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // 添加标题
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                Size = new Size(size.Width, 30),
                Location = new Point(0, 5),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.LightGray
            };
            panel.Controls.Add(titleLabel);

            // 创建图表绘制区域
            var chartArea = new Panel
            {
                Location = new Point(0, 30),
                Size = new Size(size.Width, size.Height - 30),
                BackColor = Color.White
            };
            panel.Controls.Add(chartArea);

            return chartArea; // 返回图表绘制区域
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 创建图表控件
            CreateChartControls();
        }

        /// <summary>
        /// 创建图表控件
        /// </summary>
        private void CreateChartControls()
        {
            // 创建类别1图表
            category1Chart = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            category1Chart.Paint += Category1Chart_Paint;
            pnlCategory1Chart.Controls.Add(category1Chart);

            // 创建类别2图表
            category2Chart = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            category2Chart.Paint += Category2Chart_Paint;
            pnlCategory2Chart.Controls.Add(category2Chart);

            // 创建因子图表
            factorChart = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            factorChart.Paint += FactorChart_Paint;
            pnlFactorChart.Controls.Add(factorChart);

            // 创建总故障率图表
            totalChart = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            totalChart.Paint += TotalChart_Paint;
            pnlTotalChart.Controls.Add(totalChart);
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            CalculateAndDisplayStatistics();
        }

        /// <summary>
        /// 计算并显示统计结果
        /// </summary>
        private void CalculateAndDisplayStatistics()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                _statisticsData = CalculateFaultStatistics();

                // 显示统计结果
                DisplayStatisticsResults();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"统计计算失败：{ex.Message}");
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        #region 图表绘制事件

        /// <summary>
        /// 类别1图表绘制事件
        /// </summary>
        private void Category1Chart_Paint(object sender, PaintEventArgs e)
        {
            if (_statisticsData == null) return;

            var category1Data = _statisticsData.Where(x => x.StatisticsType == "类别1").ToList();
            DrawBarChart(e.Graphics, category1Data, category1Chart.ClientRectangle);
        }

        /// <summary>
        /// 类别2图表绘制事件
        /// </summary>
        private void Category2Chart_Paint(object sender, PaintEventArgs e)
        {
            if (_statisticsData == null) return;

            var category2Data = _statisticsData.Where(x => x.StatisticsType == "类别2").ToList();
            DrawBarChart(e.Graphics, category2Data, category2Chart.ClientRectangle);
        }

        /// <summary>
        /// 因子图表绘制事件
        /// </summary>
        private void FactorChart_Paint(object sender, PaintEventArgs e)
        {
            if (_statisticsData == null) return;

            var factorData = _statisticsData.Where(x => x.StatisticsType == "因子").ToList();
            DrawBarChart(e.Graphics, factorData, factorChart.ClientRectangle);
        }

        /// <summary>
        /// 总故障率图表绘制事件
        /// </summary>
        private void TotalChart_Paint(object sender, PaintEventArgs e)
        {
            if (_statisticsData == null) return;

            var totalData = _statisticsData.Where(x => x.StatisticsType == "总计").FirstOrDefault();
            if (totalData != null)
            {
                DrawPieChart(e.Graphics, totalData, totalChart.ClientRectangle);
            }
        }

        #endregion

        /// <summary>
        /// 计算故障统计数据
        /// </summary>
        /// <returns>统计结果列表</returns>
        private List<FaultStatisticsItem> CalculateFaultStatistics()
        {
            var statisticsItems = new List<FaultStatisticsItem>();

            // 获取指定时间段内的故障记录
            var faultRecords = GetFaultRecordsInPeriod();

            // 获取所有设备信息
            var allDevices = GetAllDevices();

            // 计算按类别1统计的故障率
            var category1Statistics = CalculateCategory1Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category1Statistics);

            // 计算按类别2统计的故障率
            var category2Statistics = CalculateCategory2Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category2Statistics);

            // 计算按因子统计的故障率
            var factorStatistics = CalculateFactorStatistics(faultRecords, allDevices);
            statisticsItems.AddRange(factorStatistics);

            // 计算总故障率
            var totalStatistics = CalculateTotalStatistics(faultRecords, allDevices);
            statisticsItems.Add(totalStatistics);

            return statisticsItems;
        }

        /// <summary>
        /// 获取指定时间段内的故障记录
        /// </summary>
        /// <returns>故障记录列表</returns>
        private List<FaultRecordData> GetFaultRecordsInPeriod()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<FaultRecordData>()
                     .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime)
                     .ToList();
        }

        /// <summary>
        /// 获取所有设备信息
        /// </summary>
        /// <returns>设备信息列表</returns>
        private List<DeviceInfo> GetAllDevices()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<DeviceInfo>().ToList();
        }

        /// <summary>
        /// 计算按类别1统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别1统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory1Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                var faultCount = faultRecords.Count(f => f.Category1Id == category1.FaultId);
                var totalDeviceCount = allDevices.Count;
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别1",
                    ItemName = category1.FaultName,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics;
        }

        /// <summary>
        /// 计算按类别2统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别2统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory2Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                foreach (var category2 in category1.SubCategories)
                {
                    var faultCount = faultRecords.Count(f => f.Category2Id == category2.FaultId);
                    var totalDeviceCount = allDevices.Count;
                    var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                    statistics.Add(new FaultStatisticsItem
                    {
                        StatisticsType = "类别2",
                        ItemName = $"{category1.FaultName}-{category2.FaultName}",
                        FaultCount = faultCount,
                        TotalDeviceCount = totalDeviceCount,
                        FaultRate = faultRate
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// 计算按因子统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>因子统计结果</returns>
        private List<FaultStatisticsItem> CalculateFactorStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();

            // 按因子分组统计
            var factorGroups = new Dictionary<string, List<string>>();
            var factorDeviceCounts = new Dictionary<string, int>();

            // 遍历所有设备，按因子分组
            foreach (var device in allDevices)
            {
                try
                {
                    var factor = GetFactorFromSNCode(device.SNCode);
                    if (!factorGroups.ContainsKey(factor))
                    {
                        factorGroups[factor] = new List<string>();
                        factorDeviceCounts[factor] = 0;
                    }
                    factorGroups[factor].Add(device.SNCode);
                    factorDeviceCounts[factor]++;
                }
                catch
                {
                    // 忽略获取因子失败的设备
                }
            }

            // 计算每个因子的故障率
            foreach (var factorGroup in factorGroups)
            {
                var factor = factorGroup.Key;
                var deviceSNCodes = factorGroup.Value;
                var totalDeviceCount = factorDeviceCounts[factor];

                // 统计该因子下的故障数量
                var faultCount = faultRecords.Count(f => deviceSNCodes.Contains(f.SNCode));
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "因子",
                    ItemName = factor,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics.OrderBy(s => s.ItemName).ToList();
        }

        /// <summary>
        /// 计算总故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>总故障率统计结果</returns>
        private FaultStatisticsItem CalculateTotalStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var totalDeviceCount = allDevices.Count;

            // 统计有故障的设备数量（去重）
            var faultDeviceSNCodes = faultRecords.Select(f => f.SNCode).Distinct().ToList();
            var faultDeviceCount = faultDeviceSNCodes.Count;

            var totalFaultRate = totalDeviceCount > 0 ? (double)faultDeviceCount / totalDeviceCount * 100 : 0;

            return new FaultStatisticsItem
            {
                StatisticsType = "总计",
                ItemName = "总故障率",
                FaultCount = faultDeviceCount,
                TotalDeviceCount = totalDeviceCount,
                FaultRate = totalFaultRate
            };
        }

        /// <summary>
        /// 根据设备序列号获取因子信息
        /// </summary>
        /// <param name="snCode">设备序列号</param>
        /// <returns>因子信息</returns>
        private string GetFactorFromSNCode(string snCode)
        {
            if (string.IsNullOrEmpty(snCode))
                return "未知";

            try
            {
                // 根据序列号前6位获取设备配置
                if (snCode.Length >= 6)
                {
                    string idCode = snCode.Substring(0, 6);
                    var deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(idCode);
                    if (deviceConfig != null)
                    {
                        return deviceConfig.Factor;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不影响显示
            }

            return "未知";
        }

        /// <summary>
        /// 显示统计结果
        /// </summary>
        private void DisplayStatisticsResults()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowInfo("指定时间段内没有故障数据。");
                return;
            }

            // 刷新所有图表
            RefreshAllCharts();
        }

        /// <summary>
        /// 刷新所有图表
        /// </summary>
        private void RefreshAllCharts()
        {
            category1Chart?.Invalidate();
            category2Chart?.Invalidate();
            factorChart?.Invalidate();
            totalChart?.Invalidate();
        }

        #region 图表绘制方法

        /// <summary>
        /// 绘制柱状图
        /// </summary>
        /// <param name="graphics">绘图对象</param>
        /// <param name="data">数据列表</param>
        /// <param name="rect">绘制区域</param>
        private void DrawBarChart(Graphics graphics, List<FaultStatisticsItem> data, Rectangle rect)
        {
            if (data == null || data.Count == 0) return;

            // 设置绘图区域边距
            int margin = 40;
            int chartWidth = rect.Width - 2 * margin;
            int chartHeight = rect.Height - 2 * margin;
            int chartX = rect.X + margin;
            int chartY = rect.Y + margin;

            // 计算最大故障率用于缩放
            double maxRate = data.Max(x => x.FaultRate);
            if (maxRate == 0) maxRate = 1; // 避免除零

            // 计算柱子宽度和间距
            int barCount = data.Count;
            int barWidth = Math.Max(20, chartWidth / (barCount * 2));
            int barSpacing = Math.Max(5, (chartWidth - barCount * barWidth) / (barCount + 1));

            // 绘制坐标轴
            using (var pen = new Pen(Color.Black, 2))
            {
                // Y轴
                graphics.DrawLine(pen, chartX, chartY, chartX, chartY + chartHeight);
                // X轴
                graphics.DrawLine(pen, chartX, chartY + chartHeight, chartX + chartWidth, chartY + chartHeight);
            }

            // 绘制柱子
            var colors = new Color[] { Color.FromArgb(54, 162, 235), Color.FromArgb(255, 99, 132),
                                     Color.FromArgb(255, 205, 86), Color.FromArgb(75, 192, 192),
                                     Color.FromArgb(153, 102, 255), Color.FromArgb(255, 159, 64) };

            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                int barHeight = (int)(item.FaultRate / maxRate * chartHeight * 0.8);
                int barX = chartX + barSpacing + i * (barWidth + barSpacing);
                int barY = chartY + chartHeight - barHeight;

                // 绘制柱子
                using (var brush = new SolidBrush(colors[i % colors.Length]))
                {
                    graphics.FillRectangle(brush, barX, barY, barWidth, barHeight);
                }

                // 绘制边框
                using (var pen = new Pen(Color.Black, 1))
                {
                    graphics.DrawRectangle(pen, barX, barY, barWidth, barHeight);
                }

                // 绘制数值标签
                string label = $"{item.FaultRate:F1}%";
                using (var font = new Font("微软雅黑", 8))
                using (var brush = new SolidBrush(Color.Black))
                {
                    var labelSize = graphics.MeasureString(label, font);
                    graphics.DrawString(label, font, brush,
                        barX + (barWidth - labelSize.Width) / 2,
                        barY - labelSize.Height - 2);
                }

                // 绘制X轴标签（项目名称）
                using (var font = new Font("微软雅黑", 7))
                using (var brush = new SolidBrush(Color.Black))
                {
                    string itemName = item.ItemName.Length > 8 ? item.ItemName.Substring(0, 8) + "..." : item.ItemName;
                    var labelSize = graphics.MeasureString(itemName, font);
                    graphics.DrawString(itemName, font, brush,
                        barX + (barWidth - labelSize.Width) / 2,
                        chartY + chartHeight + 5);
                }
            }

            // 绘制Y轴刻度
            using (var font = new Font("微软雅黑", 8))
            using (var brush = new SolidBrush(Color.Black))
            {
                for (int i = 0; i <= 5; i++)
                {
                    double value = maxRate * i / 5;
                    int y = chartY + chartHeight - (int)(chartHeight * 0.8 * i / 5);
                    graphics.DrawString($"{value:F1}%", font, brush, chartX - 35, y - 8);

                    // 绘制刻度线
                    using (var pen = new Pen(Color.LightGray, 1))
                    {
                        graphics.DrawLine(pen, chartX, y, chartX + chartWidth, y);
                    }
                }
            }
        }

        /// <summary>
        /// 绘制饼图
        /// </summary>
        /// <param name="graphics">绘图对象</param>
        /// <param name="data">总故障率数据</param>
        /// <param name="rect">绘制区域</param>
        private void DrawPieChart(Graphics graphics, FaultStatisticsItem data, Rectangle rect)
        {
            if (data == null) return;

            // 设置绘图区域
            int margin = 40;
            int size = Math.Min(rect.Width, rect.Height) - 2 * margin;
            int centerX = rect.X + rect.Width / 2;
            int centerY = rect.Y + rect.Height / 2;
            int radius = size / 2;

            Rectangle pieRect = new Rectangle(centerX - radius, centerY - radius, size, size);

            // 计算角度
            double faultRate = data.FaultRate;
            double normalRate = 100 - faultRate;

            float faultAngle = (float)(faultRate * 360 / 100);
            float normalAngle = (float)(normalRate * 360 / 100);

            // 绘制饼图
            using (var faultBrush = new SolidBrush(Color.FromArgb(255, 99, 132)))
            using (var normalBrush = new SolidBrush(Color.FromArgb(75, 192, 192)))
            using (var pen = new Pen(Color.Black, 2))
            {
                // 绘制故障部分
                if (faultAngle > 0)
                {
                    graphics.FillPie(faultBrush, pieRect, 0, faultAngle);
                    graphics.DrawPie(pen, pieRect, 0, faultAngle);
                }

                // 绘制正常部分
                if (normalAngle > 0)
                {
                    graphics.FillPie(normalBrush, pieRect, faultAngle, normalAngle);
                    graphics.DrawPie(pen, pieRect, faultAngle, normalAngle);
                }
            }

            // 绘制图例
            int legendY = centerY + radius + 20;
            using (var font = new Font("微软雅黑", 10))
            using (var brush = new SolidBrush(Color.Black))
            {
                // 故障设备图例
                using (var faultBrush = new SolidBrush(Color.FromArgb(255, 99, 132)))
                {
                    graphics.FillRectangle(faultBrush, centerX - 80, legendY, 15, 15);
                    graphics.DrawString($"故障设备: {data.FaultCount}台 ({faultRate:F1}%)",
                        font, brush, centerX - 60, legendY);
                }

                // 正常设备图例
                using (var normalBrush = new SolidBrush(Color.FromArgb(75, 192, 192)))
                {
                    graphics.FillRectangle(normalBrush, centerX - 80, legendY + 25, 15, 15);
                    int normalCount = data.TotalDeviceCount - data.FaultCount;
                    graphics.DrawString($"正常设备: {normalCount}台 ({normalRate:F1}%)",
                        font, brush, centerX - 60, legendY + 25);
                }
            }

            // 绘制中心文字
            using (var font = new Font("微软雅黑", 12, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.Black))
            {
                string centerText = $"总故障率\n{faultRate:F1}%";
                var textSize = graphics.MeasureString(centerText, font);
                graphics.DrawString(centerText, font, brush,
                    centerX - textSize.Width / 2, centerY - textSize.Height / 2);
            }
        }

        #endregion

        #endregion
    }
}