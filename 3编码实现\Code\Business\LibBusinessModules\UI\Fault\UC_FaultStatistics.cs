﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.DB;
using LibBusinessModules.Config;
using LibBusinessModules.Config.FaultManagement.Models;
using LibBusinessModules.UI.Fault.Models;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.WinForms;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障统计界面
    /// </summary>
    public partial class UC_FaultStatistics : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime StartTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime EndTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<FaultStatisticsItem> _statisticsData;

        /// <summary>
        /// 类别1图表控件
        /// </summary>
        private CartesianChart category1Chart;

        /// <summary>
        /// 类别2图表控件
        /// </summary>
        private CartesianChart category2Chart;

        /// <summary>
        /// 因子图表控件
        /// </summary>
        private CartesianChart factorChart;

        /// <summary>
        /// 总故障率图表控件
        /// </summary>
        private PieChart totalChart;

        #endregion

        #region 构造函数

        public UC_FaultStatistics()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 事件处理

        private void UC_FaultRecordQuery_Load(object sender, EventArgs e)
        {
            if(!_hasInit)
            {
                _hasInit = true;
                InitialDateTimePicker();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnStartQuery_Click(object sender, EventArgs e)
        {
            StartTime = dtpStartTime.Value;
            EndTime = dtpEndTime.Value;

            try
            {
                if(StartTime >= EndTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError(ex.Message);
                return;
            }

            Enabled = false;

            try
            {
                QueryResult();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }

            Enabled = true;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExcelPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出图片，请稍候...");

                ExportToPNG();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            dtpStartTime.Value = DateTime.Today;
            dtpEndTime.Value = DateTime.Today + new TimeSpan(23, 59, 59);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 创建图表控件
            CreateChartControls();
        }

        /// <summary>
        /// 创建图表控件
        /// </summary>
        private void CreateChartControls()
        {
            // 创建类别1柱状图
            category1Chart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            pnlCategory1Chart.Controls.Add(category1Chart);

            // 创建类别2柱状图
            category2Chart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            pnlCategory2Chart.Controls.Add(category2Chart);

            // 创建因子柱状图
            factorChart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            pnlFactorChart.Controls.Add(factorChart);

            // 创建总故障率饼图
            totalChart = new PieChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            pnlTotalChart.Controls.Add(totalChart);
        }

        /// <summary>
        /// 得到查询结果并更新界面
        /// </summary>
        private void QueryResult()
        {
            CalculateAndDisplayStatistics();
        }

        /// <summary>
        /// 计算并显示统计结果
        /// </summary>
        private void CalculateAndDisplayStatistics()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                _statisticsData = CalculateFaultStatistics();

                // 显示统计结果
                DisplayStatisticsResults();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"统计计算失败：{ex.Message}");
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 计算故障统计数据
        /// </summary>
        /// <returns>统计结果列表</returns>
        private List<FaultStatisticsItem> CalculateFaultStatistics()
        {
            var statisticsItems = new List<FaultStatisticsItem>();

            // 获取指定时间段内的故障记录
            var faultRecords = GetFaultRecordsInPeriod();

            // 获取所有设备信息
            var allDevices = GetAllDevices();

            // 计算按类别1统计的故障率
            var category1Statistics = CalculateCategory1Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category1Statistics);

            // 计算按类别2统计的故障率
            var category2Statistics = CalculateCategory2Statistics(faultRecords, allDevices);
            statisticsItems.AddRange(category2Statistics);

            // 计算按因子统计的故障率
            var factorStatistics = CalculateFactorStatistics(faultRecords, allDevices);
            statisticsItems.AddRange(factorStatistics);

            // 计算总故障率
            var totalStatistics = CalculateTotalStatistics(faultRecords, allDevices);
            statisticsItems.Add(totalStatistics);

            return statisticsItems;
        }

        /// <summary>
        /// 获取指定时间段内的故障记录
        /// </summary>
        /// <returns>故障记录列表</returns>
        private List<FaultRecordData> GetFaultRecordsInPeriod()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<FaultRecordData>()
                     .Where(data => data.RecordTime >= StartTime && data.RecordTime <= EndTime)
                     .ToList();
        }

        /// <summary>
        /// 获取所有设备信息
        /// </summary>
        /// <returns>设备信息列表</returns>
        private List<DeviceInfo> GetAllDevices()
        {
            var db = DBHelper.GetPCDBContext();
            return db.Queryable<DeviceInfo>().ToList();
        }

        /// <summary>
        /// 计算按类别1统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别1统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory1Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                var faultCount = faultRecords.Count(f => f.Category1Id == category1.FaultId);
                var totalDeviceCount = allDevices.Count;
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "类别1",
                    ItemName = category1.FaultName,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics;
        }

        /// <summary>
        /// 计算按类别2统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>类别2统计结果</returns>
        private List<FaultStatisticsItem> CalculateCategory2Statistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();
            var faultManager = FaultManager.GetInstance();
            var category1List = faultManager.GetAllCategory1();

            foreach (var category1 in category1List)
            {
                foreach (var category2 in category1.SubCategories)
                {
                    var faultCount = faultRecords.Count(f => f.Category2Id == category2.FaultId);
                    var totalDeviceCount = allDevices.Count;
                    var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                    statistics.Add(new FaultStatisticsItem
                    {
                        StatisticsType = "类别2",
                        ItemName = $"{category1.FaultName}-{category2.FaultName}",
                        FaultCount = faultCount,
                        TotalDeviceCount = totalDeviceCount,
                        FaultRate = faultRate
                    });
                }
            }

            return statistics;
        }

        /// <summary>
        /// 计算按因子统计的故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>因子统计结果</returns>
        private List<FaultStatisticsItem> CalculateFactorStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var statistics = new List<FaultStatisticsItem>();

            // 按因子分组统计
            var factorGroups = new Dictionary<string, List<string>>();
            var factorDeviceCounts = new Dictionary<string, int>();

            // 遍历所有设备，按因子分组
            foreach (var device in allDevices)
            {
                try
                {
                    var factor = GetFactorFromSNCode(device.SNCode);
                    if (!factorGroups.ContainsKey(factor))
                    {
                        factorGroups[factor] = new List<string>();
                        factorDeviceCounts[factor] = 0;
                    }
                    factorGroups[factor].Add(device.SNCode);
                    factorDeviceCounts[factor]++;
                }
                catch
                {
                    // 忽略获取因子失败的设备
                }
            }

            // 计算每个因子的故障率
            foreach (var factorGroup in factorGroups)
            {
                var factor = factorGroup.Key;
                var deviceSNCodes = factorGroup.Value;
                var totalDeviceCount = factorDeviceCounts[factor];

                // 统计该因子下的故障数量
                var faultCount = faultRecords.Count(f => deviceSNCodes.Contains(f.SNCode));
                var faultRate = totalDeviceCount > 0 ? (double)faultCount / totalDeviceCount * 100 : 0;

                statistics.Add(new FaultStatisticsItem
                {
                    StatisticsType = "因子",
                    ItemName = factor,
                    FaultCount = faultCount,
                    TotalDeviceCount = totalDeviceCount,
                    FaultRate = faultRate
                });
            }

            return statistics.OrderBy(s => s.ItemName).ToList();
        }

        /// <summary>
        /// 计算总故障率
        /// </summary>
        /// <param name="faultRecords">故障记录</param>
        /// <param name="allDevices">所有设备</param>
        /// <returns>总故障率统计结果</returns>
        private FaultStatisticsItem CalculateTotalStatistics(List<FaultRecordData> faultRecords, List<DeviceInfo> allDevices)
        {
            var totalDeviceCount = allDevices.Count;

            // 统计有故障的设备数量（去重）
            var faultDeviceSNCodes = faultRecords.Select(f => f.SNCode).Distinct().ToList();
            var faultDeviceCount = faultDeviceSNCodes.Count;

            var totalFaultRate = totalDeviceCount > 0 ? (double)faultDeviceCount / totalDeviceCount * 100 : 0;

            return new FaultStatisticsItem
            {
                StatisticsType = "总计",
                ItemName = "总故障率",
                FaultCount = faultDeviceCount,
                TotalDeviceCount = totalDeviceCount,
                FaultRate = totalFaultRate
            };
        }

        /// <summary>
        /// 根据设备序列号获取因子信息
        /// </summary>
        /// <param name="snCode">设备序列号</param>
        /// <returns>因子信息</returns>
        private string GetFactorFromSNCode(string snCode)
        {
            if (string.IsNullOrEmpty(snCode))
                return "未知";

            try
            {
                // 根据序列号前6位获取设备配置
                if (snCode.Length >= 6)
                {
                    string idCode = snCode.Substring(0, 6);
                    var deviceConfig = DeviceManager.GetInstance().GetDeviceConfig(idCode);
                    if (deviceConfig != null)
                    {
                        return deviceConfig.Factor;
                    }
                }
            }
            catch (Exception)
            {
                // 记录错误但不影响显示
            }

            return "未知";
        }

        /// <summary>
        /// 显示统计结果
        /// </summary>
        private void DisplayStatisticsResults()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowInfo("指定时间段内没有故障数据。");
                return;
            }

            // 更新所有图表
            UpdateCategory1Chart();
            UpdateCategory2Chart();
            UpdateFactorChart();
            UpdateTotalChart();
        }

        /// <summary>
        /// 更新类别1图表
        /// </summary>
        private void UpdateCategory1Chart()
        {
            var category1Data = _statisticsData.Where(x => x.StatisticsType == "类别1").ToList();
            if (category1Data.Count == 0) return;

            var series = new ColumnSeries<double>
            {
                Values = category1Data.Select(x => x.FaultRate).ToArray(),
                Name = "故障率(%)",
                Fill = new SolidColorPaint(SKColors.DodgerBlue),
                Stroke = new SolidColorPaint(SKColors.DarkBlue) { StrokeThickness = 1 },
                DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                DataLabelsSize = 12,
                DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                DataLabelsFormatter = point => $"{point.Coordinate.PrimaryValue:F1}%"
            };

            category1Chart.Series = new ISeries[] { series };
            category1Chart.XAxes = new[]
            {
                new Axis
                {
                    Labels = category1Data.Select(x => x.ItemName).ToArray(),
                    LabelsRotation = -45,
                    TextSize = 10
                }
            };
            category1Chart.YAxes = new[]
            {
                new Axis
                {
                    Name = "故障率(%)",
                    TextSize = 10,
                    MinLimit = 0
                }
            };
        }

        /// <summary>
        /// 更新类别2图表
        /// </summary>
        private void UpdateCategory2Chart()
        {
            var category2Data = _statisticsData.Where(x => x.StatisticsType == "类别2").ToList();
            if (category2Data.Count == 0) return;

            var series = new ColumnSeries<double>
            {
                Values = category2Data.Select(x => x.FaultRate).ToArray(),
                Name = "故障率(%)",
                Fill = new SolidColorPaint(SKColors.Crimson),
                Stroke = new SolidColorPaint(SKColors.DarkRed) { StrokeThickness = 1 },
                DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                DataLabelsSize = 12,
                DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                DataLabelsFormatter = point => $"{point.Coordinate.PrimaryValue:F1}%"
            };

            category2Chart.Series = new ISeries[] { series };
            category2Chart.XAxes = new[]
            {
                new Axis
                {
                    Labels = category2Data.Select(x => x.ItemName.Length > 10 ? x.ItemName.Substring(0, 10) + "..." : x.ItemName).ToArray(),
                    LabelsRotation = -45,
                    TextSize = 9
                }
            };
            category2Chart.YAxes = new[]
            {
                new Axis
                {
                    Name = "故障率(%)",
                    TextSize = 10,
                    MinLimit = 0
                }
            };
        }

        /// <summary>
        /// 更新因子图表
        /// </summary>
        private void UpdateFactorChart()
        {
            var factorData = _statisticsData.Where(x => x.StatisticsType == "因子").ToList();
            if (factorData.Count == 0) return;

            var series = new ColumnSeries<double>
            {
                Values = factorData.Select(x => x.FaultRate).ToArray(),
                Name = "故障率(%)",
                Fill = new SolidColorPaint(SKColors.Orange),
                Stroke = new SolidColorPaint(SKColors.DarkOrange) { StrokeThickness = 1 },
                DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                DataLabelsSize = 12,
                DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
                DataLabelsFormatter = point => $"{point.Coordinate.PrimaryValue:F1}%"
            };

            factorChart.Series = new ISeries[] { series };
            factorChart.XAxes = new[]
            {
                new Axis
                {
                    Labels = factorData.Select(x => x.ItemName).ToArray(),
                    LabelsRotation = -45,
                    TextSize = 10
                }
            };
            factorChart.YAxes = new[]
            {
                new Axis
                {
                    Name = "故障率(%)",
                    TextSize = 10,
                    MinLimit = 0
                }
            };
        }

        /// <summary>
        /// 更新总故障率图表
        /// </summary>
        private void UpdateTotalChart()
        {
            var totalData = _statisticsData.Where(x => x.StatisticsType == "总计").FirstOrDefault();
            if (totalData == null) return;

            var faultRate = totalData.FaultRate;
            var normalRate = 100 - faultRate;

            var series = new PieSeries<double>[]
            {
                new PieSeries<double>
                {
                    Values = new double[] { faultRate },
                    Name = "故障设备",
                    Fill = new SolidColorPaint(SKColors.Crimson),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.Coordinate.PrimaryValue:F1}%"
                },
                new PieSeries<double>
                {
                    Values = new double[] { normalRate },
                    Name = "正常设备",
                    Fill = new SolidColorPaint(SKColors.LimeGreen),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.Coordinate.PrimaryValue:F1}%"
                }
            };

            totalChart.Series = series;
            totalChart.Title = new LiveChartsCore.VisualElements.LabelVisual
            {
                Text = $"总故障率: {faultRate:F1}%",
                TextSize = 16,
                Padding = new LiveChartsCore.Drawing.Padding(15),
                Paint = new SolidColorPaint(SKColors.Black)
            };
        }

        #region PNG导出功能

        /// <summary>
        /// 导出统计结果为PNG图片
        /// </summary>
        private void ExportToPNG()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                UIMessageBox.ShowWarning("没有统计数据可以导出！");
                return;
            }

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 使用LiveCharts的导出功能
                    ExportChartsToImage(saveFileDialog.FileName);
                    UIMessageBox.ShowSuccess($"统计结果已成功导出到：{saveFileDialog.FileName}");
                }
                catch (Exception ex)
                {
                    throw new Exception($"保存图片文件失败：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 导出图表到图片文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private void ExportChartsToImage(string filePath)
        {
            // 创建一个大的画布来容纳所有图表
            int canvasWidth = 1200;
            int canvasHeight = 800;

            using (var bitmap = new Bitmap(canvasWidth, canvasHeight))
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.Clear(Color.White);

                // 绘制标题
                using (var titleFont = new Font("微软雅黑", 16, FontStyle.Bold))
                using (var titleBrush = new SolidBrush(Color.Black))
                {
                    string title = $"故障统计报告 ({StartTime:yyyy-MM-dd} 至 {EndTime:yyyy-MM-dd})";
                    var titleSize = graphics.MeasureString(title, titleFont);
                    graphics.DrawString(title, titleFont, titleBrush,
                        (canvasWidth - titleSize.Width) / 2, 20);
                }

                // 导出各个图表为小图片并合成
                var chartSize = new Size(580, 350);

                // 类别1图表
                if (category1Chart.Series != null && category1Chart.Series.Any())
                {
                    var chart1Image = category1Chart.GetImage(chartSize);
                    graphics.DrawImage(chart1Image, 10, 70);
                    chart1Image.Dispose();
                }

                // 类别2图表
                if (category2Chart.Series != null && category2Chart.Series.Any())
                {
                    var chart2Image = category2Chart.GetImage(chartSize);
                    graphics.DrawImage(chart2Image, 610, 70);
                    chart2Image.Dispose();
                }

                // 因子图表
                if (factorChart.Series != null && factorChart.Series.Any())
                {
                    var chart3Image = factorChart.GetImage(chartSize);
                    graphics.DrawImage(chart3Image, 10, 430);
                    chart3Image.Dispose();
                }

                // 总故障率图表
                if (totalChart.Series != null && totalChart.Series.Any())
                {
                    var chart4Image = totalChart.GetImage(chartSize);
                    graphics.DrawImage(chart4Image, 610, 430);
                    chart4Image.Dispose();
                }

                // 保存合成的图片
                bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);
            }
        }

        #endregion

        #endregion
    }
}