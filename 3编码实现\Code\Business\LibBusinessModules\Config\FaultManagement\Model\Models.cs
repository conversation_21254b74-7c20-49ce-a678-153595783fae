using System.Collections.Generic;
using System.ComponentModel;

namespace LibBusinessModules.Config.FaultManagement.Models
{
    /// <summary>
    /// 完整的故障数据结构
    /// 完整的故障数据结构，主要包含一级故障分类列表
    /// 此类作为故障配置的根节点
    /// </summary>
    public class FaultInfo
    {
        /// <summary>
        /// 一级故障分类列表
        /// </summary>
        [Description("一级故障分类列表")]
        public List<FaultCategory1> Category1List { get; set; } = new List<FaultCategory1>();
    }

    /// <summary>
    /// 故障一级分类模型
    /// </summary>
    public class FaultCategory1
    {
        /// <summary>
        /// 故障ID
        /// </summary>
        [Description("故障ID")]
        public int FaultId { get; set; }

        /// <summary>
        /// 故障名称
        /// </summary>
        [Description("故障名称")]
        public string FaultName { get; set; }

        /// <summary>
        /// 故障描述信息
        /// </summary>
        [Description("故障描述信息")]
        public string Description { get; set; }

        /// <summary>
        /// 包含的二级分类
        /// </summary>
        [Description("包含的二级分类")]
        public List<FaultCategory2> SubCategories { get; set; } = new List<FaultCategory2>();

        /// <summary>
        /// 重写ToString方法，用于ComboBox显示
        /// </summary>
        /// <returns>故障名称</returns>
        public override string ToString()
        {
            return FaultName ?? "";
        }
    }

    /// <summary>
    /// 故障二级分类模型
    /// </summary>
    public class FaultCategory2
    {
        /// <summary>
        /// 父故障ID
        /// </summary>
        [Description("父故障ID")]
        public int ParentFaultId { get; set; }

        /// <summary>
        /// 故障ID
        /// </summary>
        [Description("故障ID")]
        public int FaultId { get; set; }

        /// <summary>
        /// 故障名称
        /// </summary>
        [Description("故障名称")]
        public string FaultName { get; set; }

        /// <summary>
        /// 故障描述信息
        /// </summary>
        [Description("故障描述信息")]
        public string Description { get; set; }

        /// <summary>
        /// 重写ToString方法，用于ComboBox显示
        /// </summary>
        /// <returns>故障名称</returns>
        public override string ToString()
        {
            return FaultName ?? "";
        }
    }
}