using LibBusinessModules.Config;
using LibBusinessModules.Config.FaultManagement.Models;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using Sunny.UI;
using System;
using System.Windows.Forms;

namespace LibBusinessModules.UI.Fault
{
    /// <summary>
    /// 故障添加对话框
    /// </summary>
    public partial class FrmFaultAdd : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 添加的故障信息
        /// </summary>
        public FaultRecordData FaultInfo { get; private set; }

        #endregion

        #region 构造函数

        public FrmFaultAdd()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion


        #region 事件处理

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if(!ValidateInput())
                    return;

                // 获取选中的故障类型对象
                var selectedCategory1 = cmbCategory1.SelectedItem as FaultCategory1;
                var selectedCategory2 = cmbCategory2.SelectedItem as FaultCategory2;

                // 创建故障信息对象
                FaultInfo = new FaultRecordData
                {
                    RecordTime = dtpRecordTime.Value,
                    SNCode = deviceSelector.SelectSNCode,
                    Category1Id = selectedCategory1?.FaultId ?? 0,
                    Category2Id = selectedCategory2?.FaultId ?? 0,
                    ProblemDescription = txtProblemDescription.Text.Trim()
                };

                // 保存到数据库
                var db = DBHelper.GetPCDBContext();
                db.Insertable(FaultInfo).ExecuteCommand();

                //UIMessageBox.ShowSuccess("故障信息添加成功！");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch(Exception ex)
            {
                UIMessageBox.ShowError($"添加故障信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 类别1选择变化事件
        /// </summary>
        private void CmbCategory1_SelectedIndexChanged(object sender, EventArgs e)
        {
            cmbCategory2.Items.Clear();

            if(cmbCategory1.SelectedItem is not FaultCategory1 faultCategory1)
            {
                return;
            }

            // 根据类别1动态设置类别2选项
            foreach(var faultCategory2 in faultCategory1.SubCategories)
            {
                cmbCategory2.Items.Add(faultCategory2);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            // 初始化时间
            dtpRecordTime.Value = DateTime.Now;
            //dtpRecordTime.Enabled = false; // 时间自动录入，不允许修改

            // 初始化故障类别下拉选项（从FaultManager获取）
            InitializeFaultCategories();
        }

        /// <summary>
        /// 初始化故障类别
        /// </summary>
        private void InitializeFaultCategories()
        {
            try
            {
                var faultManager = FaultManager.GetInstance();

                // 初始化类别1下拉选项
                cmbCategory1.Items.Clear();
                var category1List = faultManager.GetAllCategory1();
                foreach(FaultCategory1 category in category1List)
                {
                    cmbCategory1.Items.Add(category);
                }

                // 初始化类别2下拉选项
                cmbCategory2.Items.Clear();
                // 类别2的选项会在类别1选择后动态更新

                // 绑定类别1选择变化事件
                cmbCategory1.SelectedIndexChanged += CmbCategory1_SelectedIndexChanged;
            }
            catch(Exception ex)
            {
                // 绑定类别1选择变化事件
                cmbCategory1.SelectedIndexChanged += CmbCategory1_SelectedIndexChanged;
            }
        }

        /// <summary>
        /// 验证输入数据
        /// </summary>
        /// <returns></returns>
        private bool ValidateInput()
        {
            if(cmbCategory1.SelectedItem is not FaultCategory1)
            {
                UIMessageBox.ShowError("请选择故障类别1！");
                cmbCategory1.Focus();
                return false;
            }

            if(cmbCategory2.SelectedItem is not FaultCategory2)
            {
                UIMessageBox.ShowError("请选择故障类别2！");
                cmbCategory2.Focus();
                return false;
            }

            //if(string.IsNullOrWhiteSpace(txtProblemDescription.Text))
            //{
            //    UIMessageBox.ShowError("请输入故障问题描述！");
            //    txtProblemDescription.Focus();
            //    return false;
            //}

            return true;
        }

        #endregion
    }
}