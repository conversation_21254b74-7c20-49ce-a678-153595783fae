﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.json\8.0.5\buildTransitive\net462\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\8.0.5\buildTransitive\net462\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)entityframework\6.4.4\buildTransitive\EntityFramework.targets" Condition="Exists('$(NuGetPackageRoot)entityframework\6.4.4\buildTransitive\EntityFramework.targets')" />
    <Import Project="$(NuGetPackageRoot)stub.system.data.sqlite.core.netframework\1.0.119\buildTransitive\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('$(NuGetPackageRoot)stub.system.data.sqlite.core.netframework\1.0.119\buildTransitive\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.win32\2.88.9\buildTransitive\net462\SkiaSharp.NativeAssets.Win32.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.win32\2.88.9\buildTransitive\net462\SkiaSharp.NativeAssets.Win32.targets')" />
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.macos\2.88.9\buildTransitive\net462\SkiaSharp.NativeAssets.macOS.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.macos\2.88.9\buildTransitive\net462\SkiaSharp.NativeAssets.macOS.targets')" />
    <Import Project="$(NuGetPackageRoot)harfbuzzsharp.nativeassets.win32\7.3.0.3\buildTransitive\net462\HarfBuzzSharp.NativeAssets.Win32.targets" Condition="Exists('$(NuGetPackageRoot)harfbuzzsharp.nativeassets.win32\7.3.0.3\buildTransitive\net462\HarfBuzzSharp.NativeAssets.Win32.targets')" />
    <Import Project="$(NuGetPackageRoot)harfbuzzsharp.nativeassets.macos\7.3.0.3\buildTransitive\net462\HarfBuzzSharp.NativeAssets.macOS.targets" Condition="Exists('$(NuGetPackageRoot)harfbuzzsharp.nativeassets.macos\7.3.0.3\buildTransitive\net462\HarfBuzzSharp.NativeAssets.macOS.targets')" />
  </ImportGroup>
</Project>