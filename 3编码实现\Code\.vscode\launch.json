{"version": "0.2.0", "configurations": [{"name": "启动项目（构建）", "type": "clr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/../Product/Debug/IAutoTestX.exe", "args": [], "cwd": "${workspaceFolder}/../Product/Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "启动项目（不构建）", "type": "clr", "request": "launch", "program": "${workspaceFolder}/../Product/Debug/IAutoTestX.exe", "args": [], "cwd": "${workspaceFolder}/../Product/Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "启动项目（重新构建）", "type": "clr", "request": "launch", "preLaunchTask": "rebuild", "program": "${workspaceFolder}/../Product/Debug/IAutoTestX.exe", "args": [], "cwd": "${workspaceFolder}/../Product/Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "附加到进程", "type": "clr", "request": "attach", "processId": "${command:pickProcess}", "justMyCode": true}], "compounds": []}